import {
  IsString,
  IsEmail,
  IsNotEmpty,
  Length,
  IsOptional,
  IsBoolean,
  IsN<PERSON>ber,
  Min,
} from "@repo/model-transformer";

export class UpdateTenantDto {
  @IsString()
  @IsOptional()
  @IsNotEmpty({ message: "Company name is required" })
  @Length(2, 100, { message: "Company name must be between 2 and 100 characters" })
  companyName?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty({ message: "Admin name is required" })
  @Length(2, 50, { message: "Admin name must be between 2 and 50 characters" })
  adminName?: string;

  @IsEmail({}, { message: "Email must be valid" })
  @IsOptional()
  @IsNotEmpty({ message: "Email is required" })
  emailId?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty({ message: "Subscription type is required" })
  subscriptionType?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty({ message: "Contact number is required" })
  contactNo?: string;

  @IsNumber({}, { message: "Cap on users must be a number" })
  @IsOptional()
  @Min(1, { message: "Cap on users must be at least 1" })
  capOnUsers?: number;

  @IsString()
  @IsOptional()
  @IsNotEmpty({ message: "Address is required" })
  @Length(5, 500, { message: "Address must be between 5 and 500 characters" })
  address?: string;

  @IsBoolean({ message: "isActive must be a boolean" })
  @IsOptional()
  isActive?: boolean;
}
