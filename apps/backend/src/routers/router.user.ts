import { Router, Request, Response } from "express";
import { <PERSON><PERSON><PERSON>ogger } from "@repo/logger-pino";
import { UserController } from "@repo/user";
/**
 * Factory function to create user router
 * This ensures the router is created after SuperTokens initialization
 */
export class UserRoute {
  private static route = Router();

  private static init() {
    UserRoute.route.get("/", UserController.listUsers);
  }

  static getRouter(): Router {
    UserRoute.init();
    return UserRoute.route;
  }
}
export function createUserRouter(): Router {
  const userController = new UserController();
  const router = Router();

  router.get("/", async (req: Request, res: Response) => {
    try {
      PinoLogger.info("Fetching all users", {
        query: req.query,
        userId: req.headers["x-user-id"],
      });

      // Simulate fetching users
      const users = [
        { id: 1, name: "<PERSON>", email: "<EMAIL>" },
        { id: 2, name: "<PERSON>", email: "<EMAIL>" },
      ];

      PinoLogger.info("Users fetched successfully", {
        count: users.length,
      });

      res.json({ success: true, data: users });
    } catch (error) {
      PinoLogger.error("Failed to fetch users", error);
      res.status(500).json({ success: false, error: "Internal server error" });
    }
  });

  router.get("/:id", async (req: Request, res: Response) => {
    try {
      const userId = req.params.id;

      PinoLogger.debug("Fetching user by ID", {
        userId,
        requestedBy: req.headers["x-user-id"],
      });

      const user = userId === "1" ? { id: 1, name: "John Doe", email: "<EMAIL>" } : null;

      if (!user) {
        PinoLogger.warn("User not found", { userId });
        return res.status(404).json({
          success: false,
          error: "User not found",
        });
      }

      PinoLogger.info("User fetched successfully", { userId });
      res.json({ success: true, data: user });
    } catch (error) {
      PinoLogger.error("Failed to fetch user", error);
      res.status(500).json({ success: false, error: "Internal server error" });
    }
  });

  router.post("/", async (req: Request, res: Response) => {
    try {
      const { name, email } = req.body;

      PinoLogger.info("Creating new user", {
        name,
        email,
        createdBy: req.headers["x-user-id"],
      });

      // Validate input
      if (!name || !email) {
        PinoLogger.warn("Invalid user data", { name, email });
        return res.status(400).json({
          success: false,
          error: "Name and email are required",
        });
      }

      // Simulate user creation
      const newUser = {
        id: Math.floor(Math.random() * 1000),
        name,
        email,
      };

      PinoLogger.info("User created successfully", {
        userId: newUser.id,
        name: newUser.name,
      });

      res.status(201).json({ success: true, data: newUser });
    } catch (error) {
      PinoLogger.error("Failed to create user", error);
      res.status(500).json({ success: false, error: "Internal server error" });
    }
  });

  /**
   * Example: Update user
   * Demonstrates debug logging
   */
  router.put("/:id", async (req: Request, res: Response) => {
    try {
      const userId = req.params.id;
      const updates = req.body;

      PinoLogger.debug("Updating user", {
        userId,
        updates,
        updatedBy: req.headers["x-user-id"],
      });

      // Simulate update
      const updatedUser = {
        id: parseInt(userId, 10),
        ...updates,
      };

      PinoLogger.info("User updated successfully", { userId });
      res.json({ success: true, data: updatedUser });
    } catch (error) {
      PinoLogger.error("Failed to update user", error);
      res.status(500).json({ success: false, error: "Internal server error" });
    }
  });

  /**
   * Example: Delete user
   * Demonstrates warn and info logging
   */
  router.delete("/:id", async (req: Request, res: Response) => {
    try {
      const userId = req.params.id;

      PinoLogger.warn("Deleting user", {
        userId,
        deletedBy: req.headers["x-user-id"],
      });

      // Simulate deletion
      PinoLogger.info("User deleted successfully", { userId });
      res.json({ success: true, message: "User deleted" });
    } catch (error) {
      PinoLogger.error("Failed to delete user", error);
      res.status(500).json({ success: false, error: "Internal server error" });
    }
  });

  router.post("/list", userController.listUsers);
  return router;
}

// Export default for backward compatibility
export default createUserRouter();
