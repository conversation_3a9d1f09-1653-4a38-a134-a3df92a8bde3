import "reflect-metadata";
import express from "express";
import { createDefaultSuperTokensService } from "./supertoken/initialize.js";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import { createAuthRouter } from "./routes/auth";
import { createTenantRouter } from "./routes/tenant";
import { createUserRouter } from "./routes/user";
import { initializeDatabaseConnection } from "./helper/database";
import { verifySession } from "supertokens-node/recipe/session/framework/express";
import { EmailService } from "./services/emailService.js";
require("dotenv").config();
import { UserController } from "@repo/user";
import { errorHandler } from "supertokens-node/framework/express";
import { metadataMiddleware } from "./middleware/metadata.js";
async function startServer() {
  const app = express();
  const port = 3005;

  // Initialize SuperTokens
  console.log("Initializing SuperTokens...");
  console.log(
    "SuperTokens Connection URI:",
    process.env.SUPERTOKENS_CONNECTION_URI || "http://localhost:3567"
  );
  console.log(
    "SuperTokens API Domain:",
    process.env.SUPERTOKENS_API_DOMAIN || "http://localhost:3005"
  );
  console.log(
    "SuperTokens Website Domain:",
    process.env.SUPERTOKENS_WEBSITE_DOMAIN || "http://localhost:3000"
  );

  const superTokensService = createDefaultSuperTokensService();
  try {
    await initializeDatabaseConnection();
  } catch (error) {
    console.error("Failed to initialize Database:", error);
    process.exit(1);
  }
  try {
    const connectionString = process.env.AZURE_COMMUNICATION_CONNECTION_STRING;
    console.log("Initializing Email service...", connectionString);
    EmailService.initialize(connectionString);
  } catch (error) {
    console.error("Failed to initialize Email service:", error);
    process.exit(1);
  }
  try {
    console.log("Calling superTokensService.init()...");
    superTokensService.init();
    console.log("SuperTokens init() completed");

    // Test if SuperTokens is actually working
    try {
      console.log("Testing SuperTokens middleware...");
      const middleware = superTokensService.getMiddleware();
      console.log("SuperTokens middleware obtained successfully");
    } catch (middlewareError) {
      console.error("Failed to get SuperTokens middleware:", middlewareError);
      console.error("SuperTokens is not properly initialized");
      console.error("Make sure SuperTokens core is running on http://localhost:3567");
      process.exit(1);
    }

    console.log("SuperTokens initialized successfully");
  } catch (error) {
    console.error("Failed to initialize SuperTokens:", error);
    console.error("Make sure SuperTokens core is running on http://localhost:3567");
    process.exit(1);
  }

  // Middleware
  app.use(
    helmet({
      contentSecurityPolicy: false, // Disable CSP for SuperTokens
    })
  );
  app.use(
    cors({
      origin: ["http://localhost:3000", "http://localhost:3567"], // Frontend and SuperTokens
      credentials: true,
    })
  );
  app.use(morgan("combined"));
  const user = new UserController();
  // IMPORTANT: Add SuperTokens middleware BEFORE other routes
  // This handles built-in SuperTokens endpoints like email verification
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  const authRoutes = createAuthRouter(superTokensService);

  app.use("/auth", authRoutes);

  app.use("/api/tenants", verifySession(), metadataMiddleware, createTenantRouter());
  // app.use("/api/users", verifySession(), metadataMiddleware, createUserRouter());
  app.use("/api/users", verifySession(), metadataMiddleware, user.getRouter());
  // Health check
  app.use((req: any, res: any, next: any) => {
    const middleware = superTokensService.getMiddleware();
    return middleware(req, res, next);
  });
  app.get("/", (req, res) => {
    res.json({
      success: true,
      message: "CadetLabs Auth Service",
      timestamp: new Date().toISOString(),
    });
  });

  app.get("/health", (req, res) => {
    res.json({
      success: true,
      message: "Auth service is healthy",
      timestamp: new Date().toISOString(),
    });
  });

  // IMPORTANT: Add SuperTokens error handler at the end
  app.use((err: any, req: any, res: any, next: any) => {
    const handler = errorHandler();
    return handler(err, req, res, next);
  });

  app.listen(port, () => {
    console.log(`Auth service running at http://localhost:${port}`);
  });
}

// startServer().catch((error) => {
//   console.error("Failed to start server:", error);
//   process.exit(1);
// });
