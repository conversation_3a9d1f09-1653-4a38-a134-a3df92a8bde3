import Ajv, { JSONSchemaType } from "ajv";
import addFormats from "ajv-formats";
import { config as dotenvConfig } from "dotenv";
import rawSchema from "./config.schema.json";

dotenvConfig();

export interface AppConfig {
  SUPERTOKENS_CONNECTION_URI: string;
  SUPERTOKENS_API_DOMAIN: string;
  SUPERTOKENS_WEBSITE_DOMAIN: string;

  NODE_ENV: "development" | "production" | "test";
  PORT: number;
  HOST: string;

  DATABASE_URL: string;
  DATABASE_MAX_CONNECTIONS: number;
  DATABASE_SSL: boolean;

  AZURE_STORAGE_CONNECTION_STRING: string;
  AZURE_STORAGE_CONTAINER_NAME: string;

  EMAIL_SMTP_HOST: string;
  EMAIL_SMTP_PORT: number;
  EMAIL_USER: string;
  EMAIL_PASSWORD: string;

  JWT_SECRET: string;
  JWT_EXPIRY: string;

  LOG_LEVEL: "debug" | "info" | "warn" | "error";
  LOG_DIRECTORY: string;
  MAX_LOG_SIZE_MB: number;
  LOG_ROTATION_INTERVAL_MINUTES: number;

  ENABLE_SWAGGER: boolean;
  ENABLE_CORS: boolean;
  CORS_ORIGIN: string;

  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
}

const schema = rawSchema as JSONSchemaType<AppConfig>;

export class AppConfig {
  private static instance: AppConfig;

  static intialize(): AppConfig {
    if (!AppConfig.instance) {
      AppConfig.instance = AppConfig.loadAndValidate();
    }
    return AppConfig.instance;
  }

  private static loadAndValidate(): AppConfig {
    const rawConfig = {
      POSTGRESQL_USER: process.env.POSTGRESQL_USER || "",
      POSTGRESQL_PASSWORD: process.env.POSTGRESQL_PASSWORD || "",
      POSTGRESQL_HOST: process.env.POSTGRESQL_HOST || "",
      POSTGRESQL_PORT: parseInt(process.env.POSTGRESQL_PORT || "5432", 10),
      POSTGRESQL_DATABASE_NAME: process.env.POSTGRESQL_DATABASE_NAME || "",
      POSTGRESQL_TABLE_SCHEMA: process.env.POSTGRESQL_TABLE_SCHEMA || "public",

      SUPERTOKENS_CONNECTION_URI: process.env.SUPERTOKENS_CONNECTION_URI || "",
      SUPERTOKENS_API_DOMAIN: process.env.SUPERTOKENS_API_DOMAIN || "",
      SUPERTOKENS_WEBSITE_DOMAIN: process.env.SUPERTOKENS_WEBSITE_DOMAIN || "",

      NODE_ENV: process.env.NODE_ENV || "development",
      PORT: parseInt(process.env.PORT || "3000", 10),
      HOST: process.env.HOST || "localhost",

      DATABASE_URL: process.env.DATABASE_URL || "",
      DATABASE_MAX_CONNECTIONS: parseInt(process.env.DATABASE_MAX_CONNECTIONS || "10", 10),
      DATABASE_SSL: process.env.DATABASE_SSL === "true",

      AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_STORAGE_CONNECTION_STRING || "",
      AZURE_STORAGE_CONTAINER_NAME: process.env.AZURE_STORAGE_CONTAINER_NAME || "logs",

      EMAIL_SMTP_HOST: process.env.EMAIL_SMTP_HOST || "",
      EMAIL_SMTP_PORT: parseInt(process.env.EMAIL_SMTP_PORT || "587", 10),
      EMAIL_USER: process.env.EMAIL_USER || "",
      EMAIL_PASSWORD: process.env.EMAIL_PASSWORD || "",

      JWT_SECRET: process.env.JWT_SECRET || "",
      JWT_EXPIRY: process.env.JWT_EXPIRY || "1h",

      LOG_LEVEL: process.env.LOG_LEVEL || "info",
      LOG_DIRECTORY: process.env.LOG_DIRECTORY || "./logs",
      MAX_LOG_SIZE_MB: parseInt(process.env.MAX_LOG_SIZE_MB || "10", 10),
      LOG_ROTATION_INTERVAL_MINUTES: parseInt(process.env.LOG_ROTATION_INTERVAL_MINUTES || "5", 10),

      ENABLE_SWAGGER: process.env.ENABLE_SWAGGER === "true",
      ENABLE_CORS: process.env.ENABLE_CORS !== "false",
      CORS_ORIGIN: process.env.CORS_ORIGIN || "*",

      RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "60000", 10),
      RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100", 10),
    };

    const ajv = new Ajv({ allErrors: true, removeAdditional: true });
    addFormats(ajv);
    const validate = ajv.compile(schema);
    const valid = validate(rawConfig);

    if (!valid) {
      const errors = validate.errors
        ?.map((e) => `${e.instancePath || "env"} ${e.message}`)
        .join(", ");
      throw new Error(`❌ Environment config validation failed: ${errors}`);
    }

    console.log("✓ Config validated and loaded");
    return Object.freeze(rawConfig) as AppConfig;
  }

  static getConfig(): AppConfig {
    return AppConfig.intialize();
  }
}
