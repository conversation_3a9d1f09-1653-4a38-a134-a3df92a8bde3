{"level":"info","time":"2025-10-01T11:07:06.261Z","pid":17762,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:07:06.600Z","pid":17762,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:07:18.531Z","pid":17762,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"76e01984-681a-4767-b754-1269e723e54b","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:07:18.797Z","pid":17762,"hostname":"Anoojs-MacBook-Air.local","limit":10,"msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:07:18.836Z","pid":17762,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"305ms","contentLength":"251","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot read properties of undefined (reading 'findMany')\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":34}}"}
{"level":"info","time":"2025-10-01T11:08:18.230Z","pid":18317,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:08:18.575Z","pid":18317,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:08:32.830Z","pid":18317,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"46167579-d74d-4e99-b9d7-57f545b9f319","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:08:33.126Z","pid":18317,"hostname":"Anoojs-MacBook-Air.local","limit":10,"msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:08:33.191Z","pid":18317,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"360ms","contentLength":"251","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot read properties of undefined (reading 'findMany')\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":52}}"}
{"level":"info","time":"2025-10-01T11:09:20.366Z","pid":18789,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:09:20.623Z","pid":18789,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:09:58.378Z","pid":18789,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"84588ed6-68d0-44f6-9962-0c7d80382235","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:09:58.614Z","pid":18789,"hostname":"Anoojs-MacBook-Air.local","limit":10,"msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:09:58.647Z","pid":18789,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"269ms","contentLength":"251","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot read properties of undefined (reading 'findMany')\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":26}}"}
{"level":"info","time":"2025-10-01T11:13:25.197Z","pid":20046,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:13:25.478Z","pid":20046,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:14:28.376Z","pid":20046,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"a0c43e4a-9406-4025-a565-64d50b618952","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:14:28.609Z","pid":20046,"hostname":"Anoojs-MacBook-Air.local","limit":10,"msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:14:28.655Z","pid":20046,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"279ms","contentLength":"251","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot read properties of undefined (reading 'findMany')\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":38}}"}
{"level":"info","time":"2025-10-01T11:14:42.440Z","pid":20523,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:14:42.692Z","pid":20523,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:14:59.476Z","pid":20523,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"c4cf773e-e85b-4702-b264-48ec028a5cb2","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:14:59.718Z","pid":20523,"hostname":"Anoojs-MacBook-Air.local","limit":10,"msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:14:59.764Z","pid":20523,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"288ms","contentLength":"251","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot read properties of undefined (reading 'findMany')\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":40}}"}
{"level":"info","time":"2025-10-01T11:15:30.431Z","pid":20874,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:15:30.689Z","pid":20874,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:16:55.857Z","pid":20874,"hostname":"Anoojs-MacBook-Air.local","signal":"SIGINT","msg":"Shutdown initiated"}
