import { getPrisma } from "@repo/database";
import { UserController } from "../controllers/userController";

export class StaffUserService {
  // Helper function to generate display name
  static generateDisplayName(firstName, lastName) {
    return `${firstName.trim()} ${lastName.trim()}`.trim();
  }

  // Get all users
  static async getAllUsers() {
    const prisma = getPrisma();

    const users = await prisma.user.findMany({
      include: {
        tenant: true, // Include related tenant info
      },
    });

    return users;
  }

  // Get user by ID
  static async getUserById(userId) {
    if (!userId) {
      throw new Error("User ID is required");
    }

    const prisma = getPrisma();

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        tenant: true,
      },
    });

    if (!user) {
      const error = new Error("User not found");
      error.statusCode = 404;
      throw error;
    }

    return user;
  }

  // Create new user
  static async createUser(userData, metadata) {
    const { firstName, lastName, displayName, emailId, role } = userData;
    const tenantId = metadata.tenantId;
    // Basic validation
    if (!firstName || !lastName || !emailId || !role) {
      const error = new Error("firstName, lastName, emailId, role, and tenantId are required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    // Check if tenant exists
    const tenant = await prisma.tenant.findFirst({
      where: { tenantId: tenantId },
    });

    if (!tenant) {
      const error = new Error("Invalid tenant ID");
      error.statusCode = 400;
      throw error;
    }

    // Check user cap
    const currentUserCount = await prisma.user.count({
      where: { tenantId: tenantId },
    });

    if (currentUserCount >= tenant.capOnUsers) {
      const error = new Error(
        `Cannot create user. Tenant has reached maximum user limit of ${tenant.capOnUsers}`
      );
      error.statusCode = 400;
      throw error;
    }

    try {
      const user = await prisma.user.create({
        data: {
          firstName,
          lastName,
          displayName: displayName || StaffUserService.generateDisplayName(firstName, lastName),
          emailId,
          role,
          tenantId,
          isActive: true,
        },
      });

      await UserController.createUserDirectly(emailId, "password123", {
        firstName,
        lastName,
        displayName: displayName || StaffUserService.generateDisplayName(firstName, lastName),
        emailId,
        role,
        tenantId,
        userId: user.id,
        firstTimeUser: true,
      });

      return user;
    } catch (error) {
      if (error.code === "P2002") {
        const customError = new Error("Email already exists");
        customError.statusCode = 400;
        throw customError;
      }
      throw error;
    }
  }

  // Update user
  static async updateUser(userId, updateData) {
    if (!userId) {
      const error = new Error("User ID is required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    // If firstName or lastName is updated but displayName is not provided, regenerate it
    if ((updateData.firstName || updateData.lastName) && !updateData.displayName) {
      const currentUser = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (currentUser) {
        const newFirstName = updateData.firstName || currentUser.firstName;
        const newLastName = updateData.lastName || currentUser.lastName;
        updateData.displayName = StaffUserService.generateDisplayName(newFirstName, newLastName);
      }
    }

    try {
      const user = await prisma.user.update({
        where: { id: userId },
        data: updateData,
      });

      return user;
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("User not found");
        customError.statusCode = 404;
        throw customError;
      } else if (error.code === "P2002") {
        const customError = new Error("Email already exists");
        customError.statusCode = 400;
        throw customError;
      }
      throw error;
    }
  }

  // Delete user (if needed)
  static async deleteUser(userId) {
    if (!userId) {
      const error = new Error("User ID is required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    try {
      const user = await prisma.user.delete({
        where: { id: userId },
      });

      return user;
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("User not found");
        customError.statusCode = 404;
        throw customError;
      }
      throw error;
    }
  }

  // This method seems to be used by other controllers, keeping it as is
  static async createUserDirectly(emailId, password, userData) {
    // Implementation for creating user directly
    // This method appears to be called by other parts of the application
    // You may need to implement this based on your existing UserController
    console.log("Creating user directly:", emailId, userData);
  }

  static async listUsers({ limit = 10, cursor }: { limit?: number; cursor?: string } = {}) {
    const prisma = getPrisma();

    const take = Math.min(Math.max(1, Number(limit) || 10), 100);

    const query: any = {
      take: take + 1,
      orderBy: { createdAt: "desc" }, // better than UUID ordering
    };

    if (cursor) {
      query.cursor = { id: cursor }; // UUID works fine here
      query.skip = 1;
    }

    const results = await prisma.user.findMany(query);

    const hasMore = results.length > take;
    const users = results.slice(0, take);
    const nextCursor = hasMore ? users[users.length - 1].id : null;

    return {
      users,
      hasMore,
      nextCursor,
    };
  }

  static async checkEmailIsActive(emailId: string) {
    const prisma = getPrisma();
    console.log("Checking if email is active:", emailId);
    const user = await prisma.user.findFirst({
      where: { emailId: emailId },
    });
    console.log("User:", user);

    return user ? user.isActive : false;
  }
}
