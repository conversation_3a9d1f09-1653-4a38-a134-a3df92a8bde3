import path from "path";
import { UploadDriver } from "./abstract";
import fs from "fs";

export class LocalDriver implements UploadDriver {
  private backupDirectory: string;

  constructor(backupDirectory: string = './logs/backup') {
    this.backupDirectory = backupDirectory;
  }

  async upload(filePath: string, fileName: string): Promise<void> {
    try {
      fs.mkdirSync(this.backupDirectory, { recursive: true });
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(this.backupDirectory, `${fileName}-${timestamp}.log`);
      
      fs.copyFileSync(filePath, backupPath);
      console.log(`[LocalDriver] Backed up to: ${backupPath}`);
    } catch (error) {
      console.error('[LocalDriver] Backup failed:', error);
      throw error;
    }
  }
}

