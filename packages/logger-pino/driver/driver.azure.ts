import { UploadDriver } from "./driver.s3";
import { BlobServiceClient } from '@azure/storage-blob';

interface AzureDriverConfig {
  connectionString: string;
  containerName: string;
}


class AzureBlobDriver implements UploadDriver {
  private blobServiceClient: BlobServiceClient;
  private containerName: string;

  constructor(config: AzureDriverConfig) {
    this.blobServiceClient = BlobServiceClient.fromConnectionString(config.connectionString);
    this.containerName = config.containerName;
  }

  async upload(filePath: string, fileName: string): Promise<void> {
    try {
      const containerClient = this.blobServiceClient.getContainerClient(this.containerName);
      
      // Create container if it doesn't exist
      await containerClient.createIfNotExists({ access: 'container' });

      // Generate unique blob name
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const blobName = `logs/${fileName}-${timestamp}.log`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      console.log(`[AzureDriver] Uploading to: ${blobName}`);
      await blockBlobClient.uploadFile(filePath);
      console.log(`[AzureDriver] Upload successful: ${blobName}`);
    } catch (error) {
      console.error('[AzureDriver] Upload failed:', error);
      throw error;
    }
  }
}