import { Request, Response } from 'express';

/**
 * Standard service request structure
 */
export interface ServiceRequest<T = any> {
  requestId: string;
  payload: T;
  context: Record<string, any>;
  metadata: Record<string, any>;
}

/**
 * Standard service response structure
 */
export interface ServiceResponse<T = any> {
  success: boolean;
  statusCode: number;
  data?: T;
  code?: string;
  message?: string;
  error?: any;
}

/**
 * Handler context extracted from Express request
 */
export interface HandlerContext {
  requestId: string;
  id?: string;
  userId?: string;
  tenantId?: string;
  metadata?: Record<string, any>;
  rbac?: {
    resource: string;
    action: string;
    allowed: boolean;
    permissions?: Array<{ resource: string; action: string }>;
    satisfiedPermission?: { resource: string; action: string };
  };
}

/**
 * Generic handler method signature
 * T = Input DTO type (from Specification decorator)
 * R = Response data type
 */
export type HandlerMethod<T = any, R = any> = (
  data: T,
  context: HandlerContext
) => Promise<ServiceResponse<R>>;

/**
 * Express controller method signature
 */
export type ControllerMethod = (
  req: Request,
  res: Response
) => Promise<void | Response>;

/**
 * Permission configuration
 */
export interface PermissionConfig {
  resource: string;
  action: string;
}

/**
 * RBAC decorator options
 */
export interface RbacOptions {
  permissions: PermissionConfig[];
  mode: 'all' | 'any'; // require all permissions or any permission
}

/**
 * Utility type to extract DTO type from Specification decorator
 */
export type ExtractDtoType<T> = T extends new () => infer U ? U : any;

/**
 * Helper to create handler context from Express request
 */
export function createHandlerContext(req: Request): HandlerContext {
  return {
    requestId: (req.headers["x-request-id"] as string) || "default-request-id",
    id: req.params.id,
    userId: req.params.userId || req.query.userId as string,
    tenantId: req.params.tenantId || req.query.tenantId as string,
    metadata: (req as any).metadata,
    rbac: (req as any).rbac,
  };
}

/**
 * Helper to send standardized responses
 */
export function sendResponse<T>(res: Response, serviceResponse: ServiceResponse<T>): void {
  res.status(serviceResponse.statusCode).json(serviceResponse);
}
