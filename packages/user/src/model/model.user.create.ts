
import { IsString, <PERSON>E<PERSON>, <PERSON>NotEmpty, Length } from "@repo/model-transformer";

export class CreateUserDto {
  @IsString()
  @IsNotEmpty({ message: "First name is required" })
  @Length(2, 50, { message: "First name must be between 2 and 50 characters" })
  firstName!: string;

  @IsString()
  @IsNotEmpty({ message: "Last name is required" })
  @Length(2, 50, { message: "Last name must be between 2 and 50 characters" })
  lastName!: string;

  @IsString()
  @IsNotEmpty({ message: "Display name is required" })
  @Length(2, 100, { message: "Display name must be between 2 and 100 characters" })
  displayName!: string;

  @IsEmail({}, { message: "Email must be valid" })
  @IsNotEmpty({ message: "Email is required" })
  emailId!: string;

  @IsString()
  @IsNotEmpty({ message: "Role is required" })
  role!: string;
}
