import { ApplicationContext } from "@repo/common";
import { Database } from "@repo/database";

export class BaseRepository<T> {
  protected entityName: string;

  constructor(entityName: string) {
    this.entityName = entityName;
  }

  protected getTenantId(): string {
    const ctx = ApplicationContext.require();
    if (!ctx.user?.tenantId) {
      throw new Error("No tenant ID found in context");
    }
    return ctx.user.tenantId;
  }

  protected getEntity() {
    const client = Database.getClient();
    return (client as any)[this.entityName];
  }

  protected getClient(): any {
    return Database.getClient();
  }

  public readonly rawQuery = async <R = any>(query: string, params?: any[]): Promise<R> => {
    const client = this.getClient();
    return (client as any).$queryRawUnsafe(query, ...(params || []));
  };

  public readonly rawExecute = async (query: string, params?: any[]): Promise<number> => {
    const client = this.getClient();
    return (client as any).$executeRawUnsafe(query, ...(params || []));
  };

  public readonly transaction = async <R>(fn: (tx: any) => Promise<R>): Promise<R> => {
    const client = this.getClient();
    return (client as any).$transaction(fn);
  };

  public readonly findMany = async (args: Record<string, any> = {}): Promise<T[]> => {
    return this.getEntity().findMany({
      where: { tenantId: this.getTenantId(), ...args.where },
      ...args,
    });
  };

  public readonly findUnique = async (args: Record<string, any>): Promise<T | null> => {
    return this.getEntity().findUnique({
      where: { ...args.where, tenantId: this.getTenantId() },
      ...args,
    });
  };

  public readonly create = async (data: Record<string, any>): Promise<T> => {
    return this.getEntity().create({
      data: { ...data, tenantId: this.getTenantId() },
    });
  };

  public readonly update = async (args: Record<string, any>): Promise<T> => {
    return this.getEntity().update({
      where: { ...args.where, tenantId: this.getTenantId() },
      data: args.data,
    });
  };

  public readonly delete = async (args: Record<string, any>): Promise<T> => {
    return this.getEntity().delete({
      where: { ...args.where, tenantId: this.getTenantId() },
    });
  };
}
