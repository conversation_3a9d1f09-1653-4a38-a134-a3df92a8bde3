import "reflect-metadata";

import { Base<PERSON>ontroller } from "@repo/abstract";
import { type Request, type Response, Router } from "express";
import { UserService } from "../service/service.user";
import { Specification } from "@repo/model-transformer";
import { CreateUserDto } from "../model/model.user.create";
import { UpdateUserDto } from "../model/model.user.update";
import { RequirePermission } from "../decorator/rbac.decorator";
export class UserController extends BaseController {
  private readonly router: Router;
  private service: UserService;

  constructor() {
    super();
    this.router = Router();
    this.service = new UserService();
  }

  @RequirePermission("user", "create")
  @Specification({ param_blueprint: CreateUserDto }, { validate: true })
  override async create(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.createUser({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: req.body,
        context: {},
        metadata: {},
      });

      return res.status(201).json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  @RequirePermission("user", "read")
  override async get(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.getUserById({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: { id: req.params.id as string },
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  @RequirePermission("user", "read")
  override async find(req: Request, res: Response): Promise<Response> {
    try {
      console.log("find users");
      console.log("req.query", req.query);
      return res.json({ message: "find users" });
      const result = await this.service.getAllUsers({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: {},
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  @RequirePermission("user", "update")
  @Specification({ param_blueprint: UpdateUserDto }, { validate: true })
  override async update(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.updateUser({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: { id: req.params.id as string, data: req.body },
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  @RequirePermission("user", "delete")
  override async delete(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.deleteUser({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: { id: req.params.id as string },
        context: {},
        metadata: {},
      });

      return res.json({ message: "User deleted successfully", user: result });
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  // Additional methods for user management
  @RequirePermission("user", "update")
  async activateUser(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.updateUser({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: { id: req.params.id as string, data: { isActive: true } },
        context: {},
        metadata: {},
      });

      return res.json({ message: "User activated successfully", user: result });
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  @RequirePermission("user", "update")
  async deactivateUser(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.updateUser({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: { id: req.params.id as string, data: { isActive: false } },
        context: {},
        metadata: {},
      });

      return res.json({ message: "User deactivated successfully", user: result });
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  @RequirePermission("user", "read")
  async listUsers(req: Request, res: Response): Promise<Response> {
    try {
      // This could be enhanced with pagination parameters from req.body
      const result = await this.service.listUsers({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: req.body || {},
        context: {},
        metadata: {},
      });
      console.log("list users result", result);
      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  getRouter(): Router {
    // Standard CRUD routes
    this.router.post("/", this.create.bind(this));
    this.router.get("/:id", this.get.bind(this));
    this.router.get("/", this.find.bind(this));
    this.router.put("/:id", this.update.bind(this));
    this.router.delete("/:id", this.delete.bind(this));

    // Additional user management routes
    this.router.post("/list", this.listUsers.bind(this));
    this.router.put("/:id/activate", this.activateUser.bind(this));
    this.router.put("/:id/deactivate", this.deactivateUser.bind(this));

    return this.router;
  }
}
