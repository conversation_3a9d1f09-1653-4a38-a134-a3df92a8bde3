import { StaffUserService } from "../services/staffUserService";
import UserMetadata from "supertokens-node/recipe/usermetadata";

export class StaffUserController {
  // Get all users
  static async getAllUsers(req, res) {
    try {
      const users = await StaffUserService.getAllUsers();
      res.json(users);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Get user by ID
  static async getUserById(req, res) {
    try {
      const user = await StaffUserService.getUserById(req.params.id);
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Create new user
  static async createUser(req, res) {
    try {
      const userId = req.session!.getUserId();
      const { metadata } = await UserMetadata.getUserMetadata(userId);
      console.log("metadata", metadata);
      const user = await StaffUserService.createUser(req.body, metadata);
      res.status(201).json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Update user
  static async updateUser(req, res) {
    try {
      const user = await StaffUserService.updateUser(req.params.id, req.body);
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Delete user (if needed)
  static async deleteUser(req, res) {
    try {
      const user = await StaffUserService.deleteUser(req.params.id);
      res.json({ message: "User deleted successfully", user });
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
  static async listUsers(req, res) {
    try {
      const { limit, cursor } = req.body;
      const result = await StaffUserService.listUsers({ limit, cursor });
      res.json(result);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  static async activateUser(req, res) {
    try {
      const user = await StaffUserService.activateUser(req.params.id);
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
  static async deactivateUser(req, res) {
    try {
      const user = await StaffUserService.deactivateUser(req.params.id);
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
  static async resendEmailVerification(req, res) {
    try {
      const user = await StaffUserService.resendEmailVerification(req.params.id);
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
}
