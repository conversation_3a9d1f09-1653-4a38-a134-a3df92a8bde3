import { BaseService, Service, ServiceError } from "@repo/abstract";
import { UpdateUserDto } from "../model/model.user.update";
import { UserRepository } from "../repo/repository.user";

interface CreateUserPayload {
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  tenantId?: string;
}

export class UserService extends BaseService<any, any> {
  private userRepository: UserRepository;

  constructor() {
    super("UserService");
    this.userRepository = new UserRepository();
  }

  public async createUser(params: Service.Params<CreateUserPayload>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      // Basic validation
      if (!payload.firstName || !payload.lastName || !payload.emailId || !payload.role) {
        throw new ServiceError(
          "firstName, lastName, emailId, and role are required",
          "VALIDATION_ERROR",
          400
        );
      }

      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(payload.emailId);

      if (existingUser) {
        throw new ServiceError(
          `User with email ${payload.emailId} already exists`,
          "USER_EXISTS",
          409
        );
      }

      // Compute display name if not provided
      const displayName =
        payload.displayName && payload.displayName.trim().length > 0
          ? payload.displayName
          : `${payload.firstName?.trim() || ""} ${payload.lastName?.trim() || ""}`.trim();

      // Create user
      try {
        const user = await this.userRepository.createUser({
          firstName: payload.firstName,
          lastName: payload.lastName,
          displayName,
          emailId: payload.emailId,
          role: payload.role,
          tenantId: payload.tenantId || "default-tenant",
          isActive: true,
          lastLoginAt: null,
          first_time_user: true,
        });

        return user;
      } catch (error: any) {
        if (error.code === "P2002") {
          throw new ServiceError("Email already exists", "EMAIL_EXISTS", 400);
        }
        throw new ServiceError("Failed to create user", "CREATE_USER_ERROR", 500, error);
      }
    });
  }

  public async getUserById(params: Service.Params<{ id: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError("User ID is required", "VALIDATION_ERROR", 400);
      }

      const user = await this.userRepository.findById(payload.id);

      if (!user) {
        throw new ServiceError("User not found", "USER_NOT_FOUND", 404);
      }

      return user;
    });
  }

  public async getAllUsers(params: Service.Params<any>) {
    return this.execute(params, async () => {
      const users = await this.userRepository.findAll();
      return users;
    });
  }

  public async listUsers(params: Service.Params<{ limit: number; cursor?: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;
      const { limit, cursor } = payload;

      const safeLimit = Math.min(Math.max(1, Number(limit) || 10), 100);
      const findArgs: any = {
        take: safeLimit + 1,
        orderBy: { createdAt: "desc" },
      };
      if (cursor) {
        findArgs.cursor = { id: cursor };
        findArgs.skip = 1;
      }

      const results = await this.userRepository.listUsersRaw(findArgs);

      const hasMore = results.length > safeLimit;
      const users = results.slice(0, safeLimit);
      const nextCursor = hasMore ? users[users.length - 1]?.id : undefined;

      return { users, nextCursor };
    });
  }

  public async updateUser(params: Service.Params<{ id: string; data: UpdateUserDto }>) {
  
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError("User ID is required", "VALIDATION_ERROR", 400);
      }

      try {
        // If name fields updated and displayName not provided, compute it here
        const shouldComputeDisplayName =
          (payload.data.firstName || payload.data.lastName) && !payload.data.displayName;
        let data = payload.data;
        if (shouldComputeDisplayName) {
          // Fetch current to merge names
          const current = await this.userRepository.findById(payload.id);
          if (current) {
            const newFirst = payload.data.firstName ?? current.firstName;
            const newLast = payload.data.lastName ?? current.lastName;
            const computed = `${(newFirst || "").trim()} ${(newLast || "").trim()}`.trim();
            data = { ...payload.data, displayName: computed } as any;
          }
        }
        const user = await this.userRepository.updateUser(payload.id, data as any);
        return user;
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new ServiceError("User not found", "USER_NOT_FOUND", 404);
        } else if (error.code === "P2002") {
          throw new ServiceError("Email already exists", "EMAIL_EXISTS", 400);
        }
        throw new ServiceError("Failed to update user", "UPDATE_USER_ERROR", 500, error);
      }
    });
  }

  public async deleteUser(params: Service.Params<{ id: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError("User ID is required", "VALIDATION_ERROR", 400);
      }

      try {
        const user = await this.userRepository.deleteUser(payload.id);
        return user;
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new ServiceError("User not found", "USER_NOT_FOUND", 404);
        }
        throw new ServiceError("Failed to delete user", "DELETE_USER_ERROR", 500, error);
      }
    });
  }
}
