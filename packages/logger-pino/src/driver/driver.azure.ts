import { BlobServiceClient } from '@azure/storage-blob';
import { UploadDriver } from './abstract';

interface AzureDriverConfig {
  connectionString: string;
  containerName: string;
}

export class AzureBlobDriver implements UploadDriver {
  private blobServiceClient: BlobServiceClient;
  private containerName: string;
  private static instance: AzureBlobDriver;

  private constructor(config: AzureDriverConfig) {
    this.blobServiceClient = BlobServiceClient.fromConnectionString(config.connectionString);
    this.containerName = config.containerName;
  }

  async upload(filePath: string, fileName: string): Promise<void> {
    try {
      const containerClient = this.blobServiceClient.getContainerClient(this.containerName);
      
      // Create container if it doesn't exist
      await containerClient.createIfNotExists({ access: 'container' });

      // Generate unique blob name
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const blobName = `logs/${fileName}-${timestamp}.log`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      console.log(`[AzureDriver] Uploading to: ${blobName}`);
      await blockBlobClient.uploadFile(filePath);
      console.log(`[AzureDriver] Upload successful: ${blobName}`);
    } catch (error) {
      console.error('[AzureDriver] Upload failed:', error);
      throw error;
    }
  }

  public static getInstance(config?: AzureDriverConfig): AzureBlobDriver {
    if (!AzureBlobDriver.instance) {
      if (!config) {
        throw new Error('AzureBlobDriver configuration required for first initialization');
      }
      AzureBlobDriver.instance = new AzureBlobDriver(config);
    }
    return AzureBlobDriver.instance;
  }

  public static initialize(config: AzureDriverConfig): AzureBlobDriver {
    if (AzureBlobDriver.instance) {
      console.warn('AzureBlobDriver already initialized. Returning existing instance.');
      return AzureBlobDriver.instance;
    }
    AzureBlobDriver.instance = new AzureBlobDriver(config);
    return AzureBlobDriver.instance;
  }
}

