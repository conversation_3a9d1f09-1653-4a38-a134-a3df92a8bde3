import { IsString, IsEmail, IsNotEmpty, Length, <PERSON><PERSON><PERSON>y, Is<PERSON>ptional, IsObject } from "@repo/model-transformer";

export class SignUpDto {
  @IsArray()
  @IsNotEmpty({ message: "Form fields are required" })
  formFields!: Array<{
    id: string;
    value: string;
  }>;
}

export class SignUpFieldsDto {
  @IsEmail({}, { message: "Email must be valid" })
  @IsNotEmpty({ message: "Email is required" })
  email!: string;

  @IsString()
  @IsNotEmpty({ message: "Password is required" })
  @Length(6, 100, { message: "Password must be between 6 and 100 characters" })
  password!: string;

  @IsObject()
  @IsOptional()
  metadata?: any;
}
