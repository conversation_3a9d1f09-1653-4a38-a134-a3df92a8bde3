import "reflect-metadata";

export * from "./transformer/decorator.transform";
export * from "./dto/decorator.dto";
export * from "class-validator";
export * from "class-transformer";

export type ErrorResponse = {
  success: false;
  statusCode: number; 
  code: string;     
  message: string;  
  error: unknown;
}

export type SuccessResponse<T = unknown> = {
  success: true;
  statusCode: number; // typically StatusCodes.OK
  data: T | null;
};