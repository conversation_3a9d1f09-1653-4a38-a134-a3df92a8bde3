import { BaseService, Service, ServiceError } from "@repo/abstract";
import { UpdateTenantDto } from "../model/model.tenant.update";
import { TenantRepository } from "../repo/repository.tenant";

interface CreateTenantPayload {
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
}

export class TenantService extends BaseService<any, any> {
  private tenantRepository: TenantRepository;

  constructor() {
    super("TenantService");
    this.tenantRepository = new TenantRepository();
  }

  public async createTenant(params: Service.Params<CreateTenantPayload>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      // Check if tenant already exists
      const existingTenant = await this.tenantRepository.findByEmail(payload.emailId);

      if (existingTenant) {
        throw new ServiceError(
          `Tenant with email ${payload.emailId} already exists`,
          "TENANT_EXISTS",
          409
        );
      }

      // Create tenant
      try {
        const tenant = await this.tenantRepository.createTenant({
          tenantId: this.tenantRepository.generateTenantId(),
          companyName: payload.companyName,
          adminName: payload.adminName,
          emailId: payload.emailId,
          subscriptionType: payload.subscriptionType,
          contactNo: payload.contactNo,
          capOnUsers: parseInt(payload.capOnUsers.toString()),
          address: payload.address,
        });

        // TODO: Create admin user for the tenant
        // This would require importing the user service
        // StaffUserService.createUser(...)

        return tenant;
      } catch (error: any) {
        if (error.code === "P2002") {
          throw new ServiceError("Email already exists", "EMAIL_EXISTS", 400);
        }
        throw new ServiceError("Failed to create tenant", "CREATE_TENANT_ERROR", 500, error);
      }
    });
  }

  public async getTenantById(params: Service.Params<{ id: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError("Tenant ID is required", "VALIDATION_ERROR", 400);
      }

      const tenant = await this.tenantRepository.findById(payload.id);

      if (!tenant) {
        throw new ServiceError("Tenant not found", "TENANT_NOT_FOUND", 404);
      }

      return tenant;
    });
  }

  public async getAllTenants(params: Service.Params<any>) {
    return this.execute(params, async () => {
      const tenants = await this.tenantRepository.findAll();
      return tenants;
    });
  }

  public async getUsersByTenant(params: Service.Params<{ tenantId: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.tenantId) {
        throw new ServiceError("Tenant ID is required", "VALIDATION_ERROR", 400);
      }

      const users = await this.tenantRepository.getUsersByTenant(payload.tenantId);
      return users;
    });
  }

  public async listTenants(params: Service.Params<{ limit: number; cursor?: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;
      const { limit, cursor } = payload;

      const safeLimit = Math.min(Math.max(1, Number(limit) || 10), 100);
      const findArgs: any = {
        take: safeLimit + 1,
        orderBy: { createdAt: "desc" },
      };
      if (cursor) {
        findArgs.cursor = { id: cursor };
        findArgs.skip = 1;
      }

      const results = await this.tenantRepository.listTenantsRaw(findArgs);

      const hasMore = results.length > safeLimit;
      const tenants = results.slice(0, safeLimit);
      const nextCursor = hasMore ? tenants[tenants.length - 1]?.id : undefined;

      return { tenants, hasMore, nextCursor };
    });
  }

  public async updateTenant(params: Service.Params<{ id: string; data: UpdateTenantDto }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError("Tenant ID is required", "VALIDATION_ERROR", 400);
      }

      try {
        const tenant = await this.tenantRepository.updateTenant(payload.id, payload.data as any);
        return tenant;
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new ServiceError("Tenant not found", "TENANT_NOT_FOUND", 404);
        } else if (error.code === "P2002") {
          throw new ServiceError("Email already exists", "EMAIL_EXISTS", 400);
        }
        throw new ServiceError("Failed to update tenant", "UPDATE_TENANT_ERROR", 500, error);
      }
    });
  }

  public async deleteTenant(params: Service.Params<{ id: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError("Tenant ID is required", "VALIDATION_ERROR", 400);
      }

      try {
        const tenant = await this.tenantRepository.deleteTenant(payload.id);
        return { message: "Tenant deleted successfully", tenant };
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new ServiceError("Tenant not found", "TENANT_NOT_FOUND", 404);
        }
        throw new ServiceError("Failed to delete tenant", "DELETE_TENANT_ERROR", 500, error);
      }
    });
  }
}
