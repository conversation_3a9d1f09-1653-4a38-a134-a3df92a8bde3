{"name": "@repo/auth", "version": "0.0.0", "private": true, "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0"}, "dependencies": {"@repo/abstract": "workspace:*", "@repo/common": "workspace:*", "@types/node": "^20.0.0", "jest": "^29.7.0", "package.json": "^2.0.1", "ts-jest": "^29.4.0", "@repo/database": "workspace:*", "express": "5.1.0", "@repo/model-transformer": "workspace:*", "reflect-metadata": "^0.1.13", "supertokens-node": "^21.0.2"}, "devDependencies": {"@prisma/client": "^5.20.0", "@repo/abstract": "workspace:*", "@repo/common": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.0.0", "tsup": "^8.2.4", "eslint": "^9.31.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "5.8.2", "@types/express": "5.0.3"}, "prisma": {"schema": "database/prisma/schema.prisma"}}