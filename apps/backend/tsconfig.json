{
  "extends": "@repo/typescript-config/base.json",
  "compilerOptions": {
    "lib": ["ES2020"],
    "module": "CommonJS",
    "target": "ES2020",
    "outDir": "./dist",
    "rootDir": "./src",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "useDefineForClassFields": false,
    "forceConsistentCasingInFileNames": true,
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true
  },
  "external": ["supertokens-node", "@supertokens-node"], // Don't bundle SuperTokens

  "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"],
  "include": ["src/**/*"]
}
