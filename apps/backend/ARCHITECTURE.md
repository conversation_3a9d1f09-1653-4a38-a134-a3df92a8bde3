# Backend Architecture

## Overview

The backend application follows a modular, class-based architecture with clear separation of concerns.

## Directory Structure

```
apps/backend/
├── src/
│   ├── app.ts                    # Main Application class
│   ├── index.ts                  # Entry point
│   ├── context/                  # Request context management
│   │   └── context.app.user.ts
│   ├── middlewares/              # Express middlewares
│   │   ├── index.ts              # Middleware initialization
│   │   ├── middleware.class.ts   # Middleware management system
│   │   ├── middleware.context.user.ts
│   │   └── middlewares.rbac.ts
│   └── routers/                  # API routes
│       ├── index.ts              # Router initialization
│       ├── router.user.ts        # User endpoints
│       └── router.tenant.ts      # Tenant endpoints
├── logs/                         # Log files (gitignored)
│   ├── app.log                   # Active log file
│   └── backup/                   # Rotated logs
├── dist/                         # Compiled output
├── package.json
├── tsconfig.json
├── tsup.config.ts
└── turbo.json
```

## Core Components

### 1. Application Class (`src/app.ts`)

The main application class that orchestrates all components.

**Responsibilities**:
- Initialize logger with environment-based configuration
- Set up middlewares
- Register routers
- Handle graceful shutdown
- Manage server lifecycle

**Key Methods**:
```typescript
class Application {
  constructor(port: number)           // Initialize app
  private initializeLogger(): void    // Set up logger
  private middlewares(): void         // Load middlewares
  private routers(): void             // Register routes
  public start(): void                // Start server
  private shutdown(signal): void      // Graceful shutdown
  public stop(): void                 // Stop server
}
```

**Initialization Flow**:
1. Create Express app
2. Initialize logger (LocalDriver for dev, AzureBlobDriver for prod)
3. Load middlewares (core, context, security, monitoring)
4. Register routers (users, tenants, health)
5. Start HTTP server
6. Set up signal handlers for graceful shutdown

### 2. Middleware System (`src/middlewares/`)

A flexible middleware management system with priority-based loading.

**Components**:

#### Middleware Class (`middleware.class.ts`)
- Manages middleware groups
- Priority-based loading
- Enable/disable individual middlewares
- Centralized middleware configuration

**Usage**:
```typescript
Middlewares.group('core', [
  { name: 'json-parser', handler: express.json(), priority: 100 },
  { name: 'url-encoded', handler: express.urlencoded(), priority: 99 },
  { name: 'logger', handler: PinoLogger.middleware(), priority: 98 }
]);

Middlewares.load(app, ['core', 'context', 'monitoring']);
```

#### Middleware Groups

1. **Core** (Priority: 100-98)
   - JSON parser
   - URL-encoded parser
   - Logger middleware

2. **Context** (Priority: 90)
   - Async context for user information
   - Request-scoped data

3. **Security** (Production only)
   - CORS
   - Helmet
   - Rate limiting
   - Authentication

4. **Monitoring** (Optional)
   - Performance metrics
   - Health checks

### 3. Router System (`src/routers/`)

RESTful API routes organized by resource.

**Structure**:
```typescript
// Each router is a separate module
router.user.ts      // User management endpoints
router.tenant.ts    // Tenant management endpoints
index.ts            // Router initialization and mounting
```

**Router Initialization** (`index.ts`):
```typescript
export function initRouters(app: Express): void {
  app.use('/api/users', userRouter);
  app.use('/api/tenants', tenantRouter);
  app.get('/health', healthCheck);
}
```

#### User Router (`router.user.ts`)

| Method | Endpoint | Description | Logging |
|--------|----------|-------------|---------|
| GET | `/api/users` | List all users | Info |
| GET | `/api/users/:id` | Get user by ID | Debug, Warn |
| POST | `/api/users` | Create user | Info, Warn |
| PUT | `/api/users/:id` | Update user | Debug |
| DELETE | `/api/users/:id` | Delete user | Warn |

#### Tenant Router (`router.tenant.ts`)

| Method | Endpoint | Description | Logging |
|--------|----------|-------------|---------|
| GET | `/api/tenants` | List all tenants | Info |
| GET | `/api/tenants/:id` | Get tenant by ID | Debug, Warn |
| POST | `/api/tenants` | Create tenant | Info, Warn |
| PATCH | `/api/tenants/:id/status` | Update status | Info |

### 4. Logger Integration

**Package**: `@repo/logger-pino`

**Features**:
- Automatic request/response logging via middleware
- Manual logging in routes (info, error, warn, debug)
- Automatic log rotation (size and time-based)
- Cloud storage upload (Azure Blob Storage)
- Graceful shutdown with log upload

**Usage in Routes**:
```typescript
router.get('/users', async (req, res) => {
  try {
    PinoLogger.info('Fetching users', { query: req.query });
    const users = await fetchUsers();
    PinoLogger.info('Users fetched', { count: users.length });
    res.json({ success: true, data: users });
  } catch (error) {
    PinoLogger.error('Failed to fetch users', error);
    res.status(500).json({ success: false });
  }
});
```

## Request Flow

```
1. HTTP Request
   ↓
2. Core Middlewares (Priority: 100-98)
   - JSON Parser
   - URL Encoder
   - Logger Middleware (logs request)
   ↓
3. Context Middlewares (Priority: 90)
   - User Context
   ↓
4. Security Middlewares (Production)
   - CORS
   - Helmet
   - Auth
   ↓
5. Router
   - Match route
   - Execute handler
   - Manual logging
   ↓
6. Response
   - Logger Middleware (logs response)
   - Send to client
```

## Configuration

### Environment Variables

```bash
# Application
NODE_ENV=development|production
PORT=3006

# Logger - Azure Storage (optional)
AZURE_STORAGE_CONNECTION_STRING=...
AZURE_CONTAINER_NAME=application-logs

# Database (to be added)
DATABASE_URL=...

# Authentication (to be added)
SUPERTOKENS_CONNECTION_URI=...
```

### Logger Configuration

**Development**:
- Driver: LocalDriver
- Log Level: debug
- Directory: ./logs
- Backup: ./logs/backup
- Max Size: 10 MB
- Rotation: 5 minutes

**Production**:
- Driver: AzureBlobDriver (with LocalDriver fallback)
- Log Level: info
- Directory: ./logs
- Storage: Azure Blob Storage
- Max Size: 10 MB
- Rotation: 5 minutes

## Error Handling

### Standard Error Response

```typescript
{
  success: false,
  error: "Error message"
}
```

### Error Logging Pattern

```typescript
try {
  // Operation
} catch (error) {
  PinoLogger.error('Operation failed', error);
  res.status(500).json({ 
    success: false, 
    error: 'Internal server error' 
  });
}
```

## Best Practices

### 1. Logging

- **Always log operations**: Start and end of operations
- **Include context**: User ID, request ID, relevant data
- **Use appropriate levels**:
  - `debug`: Detailed debugging info
  - `info`: Normal operations
  - `warn`: Potential issues, validation failures
  - `error`: Actual errors

### 2. Error Handling

- **Always catch errors**: Use try-catch in async handlers
- **Log before responding**: Log error details before sending response
- **Don't expose internals**: Send generic error messages to clients
- **Include context**: Log relevant data for debugging

### 3. Middleware Organization

- **Use groups**: Organize related middlewares
- **Set priorities**: Control execution order
- **Environment-aware**: Load different middlewares for dev/prod
- **Keep it modular**: One middleware per file

### 4. Router Organization

- **One resource per file**: Keep routers focused
- **RESTful conventions**: Follow REST principles
- **Consistent responses**: Use standard response format
- **Validation**: Validate input before processing

## Extending the Backend

### Adding a New Router

1. Create router file: `src/routers/router.resource.ts`
2. Define routes with logging
3. Export router
4. Register in `src/routers/index.ts`

```typescript
// router.resource.ts
import { Router } from 'express';
import { PinoLogger } from '@repo/logger-pino';

const router = Router();

router.get('/', async (req, res) => {
  PinoLogger.info('Fetching resources');
  // Implementation
});

export default router;

// index.ts
import resourceRouter from './router.resource';
app.use('/api/resources', resourceRouter);
```

### Adding a New Middleware

1. Create middleware file: `src/middlewares/middleware.feature.ts`
2. Implement middleware function
3. Register in `src/middlewares/index.ts`

```typescript
// middleware.feature.ts
export const featureMiddleware = (req, res, next) => {
  // Implementation
  next();
};

// index.ts
Middlewares.group('feature', [
  { name: 'feature', handler: featureMiddleware, priority: 85 }
]);
```

### Adding Database Integration

1. Install database client
2. Create database service
3. Initialize in Application class
4. Use in routers

```typescript
// app.ts
private async database(): Promise<void> {
  await initDatabase();
  PinoLogger.info('Database connected');
}
```

## Testing Strategy

### Unit Tests
- Test individual functions
- Mock dependencies
- Test error cases

### Integration Tests
- Test API endpoints
- Test middleware chain
- Test database operations

### E2E Tests
- Test complete user flows
- Test authentication
- Test error scenarios

## Deployment

### Build
```bash
pnpm build
```

### Run
```bash
NODE_ENV=production pnpm start
```

### Docker (to be added)
```dockerfile
FROM node:20-alpine
WORKDIR /app
COPY . .
RUN pnpm install
RUN pnpm build
CMD ["pnpm", "start"]
```

## Future Enhancements

1. **Database Integration**: Add Prisma/TypeORM
2. **Authentication**: Integrate SuperTokens
3. **Validation**: Add Zod/Joi validation
4. **API Documentation**: Add Swagger/OpenAPI
5. **Rate Limiting**: Add rate limiting middleware
6. **Caching**: Add Redis caching
7. **WebSockets**: Add real-time capabilities
8. **Testing**: Add comprehensive test suite
9. **Monitoring**: Add APM integration
10. **CI/CD**: Add automated deployment

