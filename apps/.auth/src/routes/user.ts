// routes/userRoutes.js
import { Router } from "express";
import { StaffUserController } from "../controllers/staffUserController";
import { UserController } from "@repo/user";

export const createUserRouter = (): Router => {
  const router = Router();

  // Initialize the new UserController
  const userController = new UserController();

  // Use the new UserController with built-in RBAC decorators
  router.use("/", userController.getRouter());

  // Keep some legacy routes for backward compatibility if needed
  // These can be removed once frontend is updated to use new endpoints
  router.post("/:id/resend-email-verification", StaffUserController.resendEmailVerification);

  return router;
};
