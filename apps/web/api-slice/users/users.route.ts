import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import instance from "../lib/instance";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: {
    id: string;
    tenantId: string;
    companyName: string;
    adminName: string;
    emailId: string;
    subscriptionType: string;
    contactNo: string;
    capOnUsers: number;
    address: string;
    createdAt: string;
    updatedAt: string;
    first_time_user: boolean;
  };
};

export type CreateUserPayload = {
  firstName: string;
  lastName: string;
  emailId: string;
  role: string;
};

type EditUserPayload = {
  userId: string;
  data: CreateUserPayload;
};

type ActivateDeactivate = {
  userId: string;
  data: {
    isActive: boolean;
  };
};

type GetUsersResponse = {
  result: {
    users: User[];
    nextCursor?: string;
  };
};

// GET request with useQuery
export function useUserData(userId: string) {
  return useQuery({
    queryKey: ["users", userId],
    queryFn: async () => {
      const response = await instance.get(`/users/${userId}`);
      return response.data;
    },
  });
}

export function useGetUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await instance.get(`/users`);
      console.log("useGetUsers query", response);
      return response.data;
    },
  });
}

export function useGetUsersList(limit = 10) {
  return useInfiniteQuery({
    queryKey: ["users"],
    initialPageParam: undefined,
    queryFn: async ({ pageParam }: { pageParam?: string }) => {
      const response = await instance.post<GetUsersResponse>(`/users/list`, {
        limit,
        cursor: pageParam,
      });
      console.log("useGetUsersList query", response.data);
      console.log("useGetUsersList query result", response.data.result);

      return response.data.result;
    },
    getNextPageParam: (lastPage) => lastPage.nextCursor ?? undefined,
  });
}

// POST request with useMutation
export function useCreateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: CreateUserPayload) => {
      const response = await instance.post("/users", userData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch users queries
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

export function useEditUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, data }: EditUserPayload) => {
      const response = await instance.put(`/users/${userId}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// ACTIVATE user
export function useActivateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.put(`/users/${userId}/activate`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// DEACTIVATE user
export function useDeactivateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.put(`/users/${userId}/deactivate`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// DELETE user
export function useDeleteUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.delete(`/users/${userId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// RESET PASSWORD User

export function useResetPasswordUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.patch(`/users/${userId}/reset-password`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// EMAIL VERIFICATION User

export function useResendEmailVerification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.post(`/users/${userId}/resend-email-verification`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}
