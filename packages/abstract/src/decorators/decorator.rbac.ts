import "reflect-metadata";
import { PermissionConfig, RbacOptions } from "../types/abstract.types";

/**
 * Enhanced RBAC Decorator for controller methods
 * Supports multiple permission configurations and different modes
 */
export function RequirePermissions(
  permissions: PermissionConfig | PermissionConfig[],
  mode: 'all' | 'any' = 'all'
): MethodDecorator {
  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const permissionArray = Array.isArray(permissions) ? permissions : [permissions];

    descriptor.value = async function (req: any, res: any, ...args: any[]) {
      try {
        // Check if metadata exists (should be set by metadataMiddleware)
        const metadata = req.metadata;
        if (!metadata) {
          return res.status(401).json({ error: "Unauthorized" });
        }

        const userRole = metadata.role;
        const userResources = metadata?.previlages?.resource || {};

        if (mode === 'all') {
          // Check all permissions - ALL must be satisfied
          for (const permission of permissionArray) {
            const allowedActions = userResources[permission.resource] || [];
            const isAllowed = allowedActions.includes("all") || allowedActions.includes(permission.action);

            if (!isAllowed) {
              return res.status(403).json({
                error: "Forbidden",
                message: `Role '${userRole}' cannot ${permission.action} on resource '${permission.resource}'`,
              });
            }
          }

          // Inject all RBAC decisions into req
          req.rbac = {
            permissions: permissionArray,
            mode,
            allowed: true,
          };
        } else {
          // Check any permission - ANY can be satisfied
          let hasPermission = false;
          let satisfiedPermission: PermissionConfig | null = null;

          for (const permission of permissionArray) {
            const allowedActions = userResources[permission.resource] || [];
            const isAllowed = allowedActions.includes("all") || allowedActions.includes(permission.action);

            if (isAllowed) {
              hasPermission = true;
              satisfiedPermission = permission;
              break;
            }
          }

          if (!hasPermission) {
            const permissionStrings = permissionArray
              .map((p) => `${p.action} on ${p.resource}`)
              .join(" OR ");
            return res.status(403).json({
              error: "Forbidden",
              message: `Role '${userRole}' requires one of: ${permissionStrings}`,
            });
          }

          req.rbac = {
            permissions: permissionArray,
            satisfiedPermission,
            mode,
            allowed: true,
          };
        }

        // Call the original method
        return await originalMethod.apply(this, [req, res, ...args]);
      } catch (err) {
        console.error("RBAC decorator error:", err);
        return res.status(500).json({ error: "Failed to check permissions" });
      }
    };

    // Store metadata for potential reflection
    const rbacOptions: RbacOptions = { permissions: permissionArray, mode };
    Reflect.defineMetadata("rbac:options", rbacOptions, target, propertyKey);

    return descriptor;
  };
}

/**
 * Convenience decorator for single permission (backward compatibility)
 */
export function RequirePermission(resource: string, action: string): MethodDecorator {
  return RequirePermissions({ resource, action }, 'all');
}

/**
 * Convenience decorator for multiple permissions that ALL must be satisfied
 */
export function RequireAllPermissions(...permissions: PermissionConfig[]): MethodDecorator {
  return RequirePermissions(permissions, 'all');
}

/**
 * Convenience decorator for multiple permissions where ANY can be satisfied
 */
export function RequireAnyPermission(...permissions: PermissionConfig[]): MethodDecorator {
  return RequirePermissions(permissions, 'any');
}


export function getRbacOptions(target: any, propertyKey: string | symbol): RbacOptions | undefined {
  return Reflect.getMetadata("rbac:options", target, propertyKey);
}

export class ResourcePermissions {
  static User = {
    Create: () => RequirePermission("user", "create"),
    Read: () => RequirePermission("user", "read"),
    Update: () => RequirePermission("user", "update"),
    Delete: () => RequirePermission("user", "delete"),
    Manage: () => RequirePermission("user", "manage"),
  };

  static Tenant = {
    Create: () => RequirePermission("tenant", "create"),
    Read: () => RequirePermission("tenant", "read"),
    Update: () => RequirePermission("tenant", "update"),
    Delete: () => RequirePermission("tenant", "delete"),
    Manage: () => RequirePermission("tenant", "manage"),
  };

  static Auth = {
    SignIn: () => RequirePermission("auth", "signin"),
    SignUp: () => RequirePermission("auth", "signup"),
    SignOut: () => RequirePermission("auth", "signout"),
    Manage: () => RequirePermission("auth", "manage"),
  };
}
