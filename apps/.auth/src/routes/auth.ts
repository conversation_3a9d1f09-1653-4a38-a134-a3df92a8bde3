import { Router } from "express";
import { verifySession } from "supertokens-node/recipe/session/framework/express";

// Import new package controllers
import { AuthController } from "@repo/auth";
import { EmailController } from "@repo/email";

// Import remaining old controllers that haven't been moved yet
import { UserController } from "../controllers/userController";
import { PasswordController } from "../controllers/passwordController";
import { SessionController } from "../controllers/sessionContoller";
import { HealthController } from "../controllers/healthController";
import { MetadataController } from "../controllers/metadataController";

export const createAuthRouter = () => {
  const router = Router();

  // Initialize the new controllers
  const authController = new AuthController();
  const emailController = new EmailController();

  // Health check routes
  router.get("/health", HealthController.healthCheck);

  // Authentication routes using new AuthController
  router.use("/", authController.getRouter());
  router.post("/get-metadata-by-email", MetadataController.getMetadataByEmail);
  router.post("/set-metadata-by-email", MetadataController.setMetadataByEmail);

  // User management routes
  router.get("/me", verifySession(), UserController.getCurrentUser);
  router.post("/create-user", UserController.createUser);
  router.get("/signup/email/exists", UserController.checkEmailExists);
  router.put("/user/metadata", verifySession(), UserController.updateUserMetadata);
  router.delete("/user/metadata", verifySession(), UserController.clearUserMetadata);

  // Email management routes using new EmailController
  router.use("/user/email", emailController.getRouter());

  // Password management routes
  router.post("/user/password/reset/token", PasswordController.sendPasswordResetEmail);

  // Session management routes (commented out - not implemented yet)
  // router.post("/generate-token", SessionController.generateSessionToken);
  // router.get("/session/info", verifySession(), SessionController.getSessionInfo);
  // router.put("/session/data", verifySession(), SessionController.updateSessionData);
  // router.put("/session/payload", verifySession(), SessionController.updateAccessTokenPayload);
  // router.post("/session/refresh", verifySession(), SessionController.refreshSession);
  // router.get("/session/user", verifySession(), SessionController.getUserSessions);
  // router.delete("/session/all", verifySession(), SessionController.revokeAllSessions);
  // router.delete("/session", SessionController.revokeSession);

  return router;
};
