import express, { type Express } from "express";
import http from "http";
import { PinoLogger, LocalDriver, AzureBlobDriver } from "@repo/logger-pino";
import { initRouters } from "./routers";
import { AppConfig } from "@repo/config";
import { Database } from "@repo/database";
import { createDefaultSuperTokensService, AuthService } from "@repo/supertoken";
import { BaseMiddleware } from "./middlewares/middlewares.base";
import cors from "cors";

class Application {
  private app: Express;
  private server: http.Server | null = null;
  private readonly port: number;
  private authService!: AuthService;

  constructor(port: number) {
    this.port = port;
    this.app = express();
  }

  /**
   * Initialize the application asynchronously
   */
  async initialize(): Promise<void> {
    console.log("1. Initializing config...");
    this.initConfig();

    console.log("2. Initializing database...");
    this.database();
    this.initCors();

    console.log("3. Initializing SMTP...");
    this.smtpClient();

    console.log("4. Initializing logger...");
    this.initializeLogger();

    console.log("5. Initializing SuperTokens...");
    await this.initSuperTokens();
    console.log("✓ SuperTokens initialized and middleware applied");

    console.log("6. Applying middlewares...");
    this.middlewares();

    console.log("7. Initializing routers...");
    this.routers();

    console.log("✓ Application initialized successfully");
    console.log("✓ Application initialized successfully");
  }

  private initConfig(): void {
    AppConfig.intialize();
  }
  private initCors(): void {
    const corsOptions = {
      origin: process.env.WEBSITE_DOMAIN || "http://localhost:3000",
      credentials: true, // IMPORTANT: Allow credentials (cookies)
      allowedHeaders: [
        "Content-Type",
        "Authorization",
        "st-auth-mode", // SuperTokens headers
        "rid",
        "fdi-version",
        "anti-csrf",
      ],
      exposedHeaders: ["front-token", "id-refresh-token", "anti-csrf"],
      methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    };

    this.app.use(cors(corsOptions));
    console.log("✓ CORS initialized");
  }
  private initializeLogger(): void {
    const isDev = process.env.NODE_ENV === "development";

    if (isDev) {
      const localDriver = new LocalDriver("./logs/backup");
      PinoLogger.initialize({
        driver: localDriver,
        logDirectory: "./logs",
        maxLogSizeMB: 10,
        level: "debug",
        rotationIntervalMinutes: 5,
      });
      console.log("✓ Logger initialized with LocalDriver");
    } else {
      const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
      if (!connectionString) {
        console.warn("⚠️  AZURE_STORAGE_CONNECTION_STRING not set, falling back to LocalDriver");
        const localDriver = new LocalDriver("./logs/backup");
        PinoLogger.initialize({
          driver: localDriver,
          logDirectory: "./logs",
          maxLogSizeMB: 10,
          level: "info",
          rotationIntervalMinutes: 5,
        });
      } else {
        const azureDriver = AzureBlobDriver.initialize({
          connectionString,
          containerName: process.env.AZURE_CONTAINER_NAME || "application-logs",
        });
        PinoLogger.initialize({
          driver: azureDriver,
          logDirectory: "./logs",
          maxLogSizeMB: 10,
          level: "info",
          rotationIntervalMinutes: 5,
        });
        console.log("✓ Logger initialized with AzureBlobDriver");
      }
    }
  }

  private async initSuperTokens(): Promise<void> {
    this.authService = createDefaultSuperTokensService();

    try {
      await this.authService.init();

      // Apply SuperTokens middleware
      this.app.use(this.authService.getMiddleware());

      console.log("✓ SuperTokens middleware applied to Express app");
    } catch (error) {
      console.error("✗ Failed to initialize SuperTokens:", error);
      throw error;
    }
  }

  private middlewares(): void {
    BaseMiddleware.init(this.app);
  }

  private routers(): void {
    initRouters(this.app);
  }

  private database(): void {
    Database.getClient();
  }

  private smtpClient(): void {}

  async start(): Promise<void> {
    // Ensure initialization is complete before starting
    await this.initialize();

    this.server = this.app.listen(this.port, () => {
      console.log(`🚀 Server running on http://localhost:${this.port}`);
      PinoLogger.info("Server started", { port: this.port });
    });

    process.on("SIGINT", () => this.shutdown("SIGINT"));
    process.on("SIGTERM", () => this.shutdown("SIGTERM"));
  }

  private async shutdown(signal: string): Promise<void> {
    console.log(`\n📦 Received ${signal}, shutting down gracefully...`);
    PinoLogger.info("Shutdown initiated", { signal });

    if (this.server) {
      this.server.close(async () => {
        await PinoLogger.shutdown();
        process.exit(0);
      });

      setTimeout(async () => {
        console.error("⚠️  Forced shutdown after timeout");
        await PinoLogger.shutdown();
        process.exit(1);
      }, 5000).unref();
    }
  }

  async stop(): Promise<void> {
    if (this.server) {
      this.server.close(async () => {
        console.log("🛑 Server stopped.");
        await PinoLogger.shutdown();
      });
    }
  }
}

export default Application;
