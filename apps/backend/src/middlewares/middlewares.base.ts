import { PinoLogger } from "@repo/logger-pino";
import cors from "cors";
import { SuperTokensService, createDefaultSuperTokensService } from "@repo/supertoken";
import Express from "express";
export class BaseMiddleware {
  static init(app: Express.Application) {
    app.use(
      cors({
        origin: ["http://localhost:3000", "http://localhost:3567"], // Frontend and SuperTokens
        credentials: true,
      })
    );
    app.use(Express.json());
    app.use(Express.urlencoded({ extended: true }));
    app.use(PinoLogger.middleware());
  }
}
