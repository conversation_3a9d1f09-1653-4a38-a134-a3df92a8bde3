import { defineConfig } from "tsup";

export default defineConfig({
  entry: ["src/index.ts"],
  dts: false,
  format: ["cjs", "esm"],
  splitting: false,
  sourcemap: true,
  clean: true,
  target: "es2022",
  treeshake: false,
  external: ["reflect-metadata"],
  tsconfig: "./tsconfig.json",
  esbuildOptions(options) {
    options.platform = "node";
    options.keepNames = true;
    options.minify = false;
  },
});
