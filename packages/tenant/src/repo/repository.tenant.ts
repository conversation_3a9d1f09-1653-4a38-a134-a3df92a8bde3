import { BaseRepository } from "@repo/abstract";

export class TenantRepository extends BaseRepository<any> {
  constructor() {
    super("tenant");
  }

  public async findByEmail(email: string): Promise<any | null> {
    const client = this.getClient();
    return client.tenant.findUnique({
      where: { emailId: email },
    });
  }

  public async findById(id: string): Promise<any | null> {
    const client = this.getClient();
    return client.tenant.findUnique({
      where: { id },
      include: {
        users: true,
      },
    });
  }

  public async findByTenantId(tenantId: string): Promise<any | null> {
    const client = this.getClient();
    return client.tenant.findUnique({
      where: { tenantId },
      include: {
        users: true,
      },
    });
  }

  public async findAll(): Promise<any[]> {
    const client = this.getClient();
    return client.tenant.findMany({
      include: {
        users: true,
      },
    });
  }

  public async createTenant(tenantData: any): Promise<any> {
    const client = this.getClient();
    return client.tenant.create({
      data: tenantData,
    });
  }

  public async updateTenant(id: string, tenantData: any): Promise<any> {
    const client = this.getClient();
    return client.tenant.update({
      where: { id },
      data: tenantData,
    });
  }

  public async deleteTenant(id: string): Promise<any> {
    const client = this.getClient();
    return client.tenant.delete({
      where: { id },
    });
  }

  public async getUsersByTenant(tenantId: string): Promise<any[]> {
    const client = this.getClient();
    return client.user.findMany({
      where: { tenantId },
      include: {
        tenant: true,
      },
    });
  }

  public async listTenantsRaw(args: any): Promise<any[]> {
    const client = this.getClient();
    return client.tenant.findMany(args);
  }

  public generateTenantId(): string {
    return `tenant_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
}
