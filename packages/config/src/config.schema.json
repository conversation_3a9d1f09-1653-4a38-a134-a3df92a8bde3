{"type": "object", "properties": {"NODE_ENV": {"type": "string", "enum": ["development", "production", "test"]}, "PORT": {"type": "number", "minimum": 1, "maximum": 65535}, "HOST": {"type": "string", "minLength": 1}, "AZURE_COMMUNICATION_CONNECTION_STRING": {"type": "string", "minLength": 1}, "LOG_LEVEL": {"type": "string", "enum": ["debug", "info", "warn", "error"]}, "LOG_DIRECTORY": {"type": "string", "minLength": 1}, "POSTGRESQL_USER": {"type": "string", "minLength": 1}, "POSTGRESQL_PASSWORD": {"type": "string", "minLength": 1}, "POSTGRESQL_HOST": {"type": "string", "minLength": 1}, "POSTGRESQL_PORT": {"type": "number"}, "POSTGRESQL_DATABASE_NAME": {"type": "string", "minLength": 1}, "POSTGRESQL_TABLE_SCHEMA": {"type": "string", "minLength": 1}, "SUPERTOKENS_CONNECTION_URI": {"type": "string", "minLength": 1}, "SUPERTOKENS_API_DOMAIN": {"type": "string", "minLength": 1}, "SUPERTOKENS_WEBSITE_DOMAIN": {"type": "string", "minLength": 1}}, "required": ["POSTGRESQL_USER", "PORT"], "additionalProperties": false}