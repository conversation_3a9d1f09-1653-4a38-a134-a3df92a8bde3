import "reflect-metadata";
import { <PERSON><PERSON><PERSON>ogger } from "@repo/logger-pino";
import { BaseController, Service } from "@repo/abstract";
import { type Request, type Response, Router } from "express";
import { TenantService } from "../service/service.tenant";
import { Specification } from "@repo/model-transformer";
import { CreateTenantDto } from "../model/model.tenant.create";
import { UpdateTenantDto } from "../model/model.tenant.update";
import { RequirePermission } from "../decorator/rbac.decorator";
import { TenantList } from "../model/model.tenant.list";

export class TenantController extends BaseController {
  private readonly router: Router;
  private service: TenantService;

  constructor() {
    super();
    this.router = Router();
    this.service = new TenantService();
  }

  @RequirePermission("tenant", "create")
  override async create(req: Request, res: Response): Promise<any> {
    const result = await this.createHandler(req.body);
    this.sendResponse(res, result);
  }

  @Specification({ param_blueprint: CreateTenantDto }, { validate: true })
  private async createHandler(data: any): Promise<any> {
    const result = await this.service.createTenant({
      payload: data,
      context: {},
    });
    return result;
  }

  @RequirePermission("tenant", "read")
  override async get(req: Request, res: Response): Promise<any> {
    const result = await this.getHandler(req.params.id as string);
    this.sendResponse(res, result);
  }

  private async getHandler(id: string): Promise<any> {
    const result = await this.service.getTenantById({
      payload: { id },
      context: {},
    });
    return result;
  }

  @RequirePermission("tenant", "read")
  override async find(req: Request, res: Response): Promise<any> {
    const result = await this.findHandler(req.body);
    this.sendResponse(res, result);
  }

  @Specification({ param_blueprint: undefined })
  private async findHandler(data: any): Promise<any> {
    const result = await this.service.getAllTenants({
      payload: data,
      context: {},
    });
    return result;
  }

  @RequirePermission("tenant", "update")
  override async update(req: Request, res: Response): Promise<any> {
    const result = await this.updateHandler(req.body);
    this.sendResponse(res, result);
  }

  @Specification({ param_blueprint: UpdateTenantDto }, { validate: true })
  private async updateHandler(data: any): Promise<any> {
    const result = await this.service.updateTenant({
      payload: data,
      context: {},
    });
    return result;
  }

  @RequirePermission("tenant", "delete")
  override async delete(req: Request, res: Response): Promise<any> {
    const result = await this.deleteHandler(req.params.id as string);
    this.sendResponse(res, result);
  }

  @Specification({ param_blueprint: undefined })
  private async deleteHandler(data: any) {
    const result = await this.service.deleteTenant({
      payload: data,
      context: {},
    });
    return result;
  }

  @RequirePermission("tenant", "read")
  async getUsersByTenant(req: Request, res: Response): Promise<any> {
    const result = await this.getUsersByTenantHandler(req.params.tenantId as string);
    this.sendResponse(res, result);
  }

  @Specification({ param_blueprint: undefined })
  private async getUsersByTenantHandler(data: any): Promise<any> {
    const result = await this.service.getUsersByTenant({
      payload: data,
      context: {},
    });
    return result;
  }

  // @RequirePermission("tenant", "read")
  async listTenants(req: Request, res: Response): Promise<any> {
    console.log("list tenants", this, req.body);
    const result = await this.listTenantsHandler(req.body);
    // this.sendResponse(res, this);
    res.json({ message: "list tenants", result });
  }

  // @Specification({ param_blueprint: TenantList }, { validate: true })
  private async listTenantsHandler(data: any): Promise<any> {
    PinoLogger.info("Listing tenants", data);
    const result = await this.service.listTenants({
      payload: data,
      context: {},
    });
    return result;
  }
}
