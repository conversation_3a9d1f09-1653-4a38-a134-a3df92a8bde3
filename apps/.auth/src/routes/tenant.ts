// routes/tenantRoutes.js
import { Router } from "express";
import { TenantController } from "@repo/tenant";
import { metadataMiddleware } from "../middleware/metadata";

export const createTenantRouter = (): Router => {
  const router = Router();

  // Initialize the new TenantController
  const tenantController = new TenantController();

  // Use the new TenantController with built-in RBAC decorators
  router.use("/", metadataMiddleware, tenantController.getRouter());

  return router;
};
