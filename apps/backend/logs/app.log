{"level":"info","time":"2025-10-01T11:17:09.694Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:17:09.713Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:17:32.154Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"GET","url":"/auth/me","path":"/auth/me","headers":{"host":"localhost:3005","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","st-auth-mode":"cookie","sec-ch-ua":"\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"","rid":"anti-csrf","sec-ch-ua-mobile":"?0","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","accept":"application/json, text/plain, */*","dnt":"1","origin":"http://localhost:3000","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:3000/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en-GB;q=0.9,en;q=0.8","cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4NjVhMmIyOTM2NDQ1NDYyZmQzYmJmNGNkMWQ2MWYwNDEyMGM4OWQ1ZiIsInBhcmVudFJlZnJlc2hUb2tlbkhhc2gxIjpudWxsLCJhbnRpQ3NyZlRva2VuIjpudWxsLCJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjMwMDUvYXV0aCIsInN0LWV2Ijp7InYiOnRydWUsInQiOjE3NTg5NTY5NTQxNTZ9fX0=; st-last-access-token-update=1759316343425","if-none-match":"W/\"1b0-GEPBpwVJNIyz9ECT3pIjv3nFGB8\""},"query":{},"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"}
{"level":"info","time":"2025-10-01T11:17:32.164Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"GET","url":"/api/tenants","path":"/api/tenants","headers":{"host":"localhost:3005","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","st-auth-mode":"cookie","sec-ch-ua":"\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"","rid":"anti-csrf","sec-ch-ua-mobile":"?0","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","accept":"application/json, text/plain, */*","dnt":"1","origin":"http://localhost:3000","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:3000/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en-GB;q=0.9,en;q=0.8","cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4NjVhMmIyOTM2NDQ1NDYyZmQzYmJmNGNkMWQ2MWYwNDEyMGM4OWQ1ZiIsInBhcmVudFJlZnJlc2hUb2tlbkhhc2gxIjpudWxsLCJhbnRpQ3NyZlRva2VuIjpudWxsLCJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjMwMDUvYXV0aCIsInN0LWV2Ijp7InYiOnRydWUsInQiOjE3NTg5NTY5NTQxNTZ9fX0=; st-last-access-token-update=1759316343425"},"query":{},"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"}
{"level":"info","time":"2025-10-01T11:17:32.165Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"GET","url":"/api/tenants","statusCode":404,"duration":"1ms","contentLength":150}
{"level":"info","time":"2025-10-01T11:17:32.383Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"GET","url":"/me","statusCode":304,"duration":"230ms","responseBody":"{\"status\":\"OK\",\"user\":{\"firstName\":\"admin\",\"lastName\":\"admin\",\"displayName\":\"admin \",\"emailId\":\"<EMAIL>\",\"tenantId\":\"tenant_cadetlabs\",\"role\":\"superadmin\",\"userId\":\"906946c8-f371-456a-ab3b-20bef98936a2\",\"firstTimeUser\":true,\"previlages\":{\"resource\":{\"tenant\":[\"all\",\"create\",\"update\",\"delete\",\"read\"],\"user\":[\"all\",\"create\",\"update\",\"delete\",\"read\"],\"project\":[\"all\",\"create\",\"update\",\"delete\",\"read\"]}},\"type\":\"tenant\"}}"}
{"level":"info","time":"2025-10-01T11:17:36.134Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"4d20ae60-**************-475b1caecf27","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:17:36.287Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","limit":10,"msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:17:36.331Z","pid":21607,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"197ms","contentLength":"251","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot read properties of undefined (reading 'findMany')\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":41}}"}
