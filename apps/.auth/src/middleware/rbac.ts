export const rbacMiddleware =
  (resource: string, action: string) => async (req: any, res: any, next: any) => {
    try {
      const metadata = req.metadata;
      if (!metadata) {
        return res.status(500).json({ error: "Unauthorized" });
      }

      const userRole = metadata.role;
      const userResources = metadata?.previlages?.resource || {};
      console.log("userResources", userResources);
      console.log("resource", resource);
      // Check if user has permissions for this resource
      const allowedActions = userResources[resource] || [];
      console.log("allowedActions", allowedActions);
      // "all" acts as a wildcard
      const isAllowed = allowedActions.includes("all") || allowedActions.includes(action);

      if (!isAllowed) {
        return res.status(403).json({
          error: "Forbidden",
          message: `Role '${userRole}' cannot ${action} on resource '${resource}'`,
        });
      }

      // 👇 Optionally inject the RBAC decision into req
      req.rbac = { resource, action, allowed: true };

      next();
    } catch (err) {
      console.error("rbac middleware error:", err);
      res.status(500).json({ error: "Failed to fetch metadata" });
    }
  };
