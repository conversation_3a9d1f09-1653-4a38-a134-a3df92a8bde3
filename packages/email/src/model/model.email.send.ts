import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON>y, <PERSON>Optional } from "@repo/model-transformer";

export class SendEmailDto {
  @IsEmail({}, { message: "Email must be valid" })
  @IsNotEmpty({ message: "Email is required" })
  email!: string;
}

export class SendEmailVerificationDto {
  @IsEmail({}, { message: "Email must be valid" })
  @IsNotEmpty({ message: "Email is required" })
  email!: string;
}

export class ResendEmailVerificationDto {
  @IsEmail({}, { message: "Email must be valid" })
  @IsNotEmpty({ message: "Email is required" })
  email!: string;
}
