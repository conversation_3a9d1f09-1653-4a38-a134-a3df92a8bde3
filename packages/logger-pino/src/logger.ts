import pino from "pino";
import { UploadDriver } from "./driver/abstract";
import { mkdirSync, statSync, unlinkSync } from "fs";
import path from "path";
import { NextFunction, type Request, type Response } from "express";

interface LoggerConfig {
  driver: UploadDriver;
  logDirectory: string;
  maxLogSizeMB: number;
  level?: string;
  rotationIntervalMinutes?: number;
}

export class PinoLogger {
  private static instance: PinoLogger;
  private logger: pino.Logger;
  private logFilePath: string;
  private maxLogSize: number;
  private driver: UploadDriver;
  private logDirectory: string;
  private isUploading: boolean = false;
  private rotationInterval: NodeJS.Timeout | null = null;
  private rotationIntervalMinutes: number;

  private constructor(config: LoggerConfig) {
    this.logDirectory = config.logDirectory;
    this.logFilePath = path.join(this.logDirectory, "app.log");
    this.maxLogSize = config.maxLogSizeMB * 1024 * 1024;
    this.driver = config.driver;
    this.rotationIntervalMinutes = config.rotationIntervalMinutes || 5;

    this.logger = pino(
      {
        level: config.level || "info",
        timestamp: pino.stdTimeFunctions.isoTime,
        formatters: {
          level: (label) => {
            return { level: label };
          },
        },
      },
      pino.destination({
        dest: this.logFilePath,
        sync: false,
        mkdir: true,
      })
    );

    this.ensureLogDirectory();
    this.startAutoRotation();
  }

  /**
   * Initialize the logger singleton
   */
  static initialize(config: LoggerConfig): PinoLogger {
    if (PinoLogger.instance) {
      console.warn("Logger already initialized. Returning existing instance.");
      return PinoLogger.instance;
    }
    PinoLogger.instance = new PinoLogger(config);
    return PinoLogger.instance;
  }

  /**
   * Get the singleton instance
   */
  static getInstance(): PinoLogger {
    if (!PinoLogger.instance) {
      throw new Error("Logger not initialized. Call initialize() first.");
    }
    return PinoLogger.instance;
  }

  /**
   * Static middleware for Express
   */
  static middleware() {
    PinoLogger.info("Logger middleware initialized"); 
    return async (req: Request, res: Response, next: NextFunction) => {
      const instance = PinoLogger.getInstance();
      const startTime = Date.now();
      instance.logger.info({
        type: "request",
        method: req.method,
        url: req.url,
        path: req.path,
        headers: req.headers,
        query: req.query,
        body: req.body,
        ip: req.ip,
        userAgent: req.get("user-agent"),
      });

      const originalSend = res.send;
      let responseBody: any;

      res.send = function (data: any) {
        responseBody = data;
        return originalSend.call(this, data);
      };

      res.on("finish", async () => {
        const duration = Date.now() - startTime;

        instance.logger.info({
          type: "response",
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          duration: `${duration}ms`,
          contentLength: res.get("content-length"),
          responseBody: responseBody,
        });

        await instance.checkAndUpload();
      });

      next();
    };
  }

  private ensureLogDirectory(): void {
    try {
      mkdirSync(this.logDirectory, { recursive: true });
    } catch (error) {
      console.error("Failed to create log directory:", error);
    }
  }

  private startAutoRotation(): void {
    const intervalMs = this.rotationIntervalMinutes * 60 * 1000;

    console.log(
      `[Logger] Auto-rotation enabled: uploading to server every ${this.rotationIntervalMinutes} minute(s)`
    );

    this.rotationInterval = setInterval(async () => {
      await this.rotateLogsByTime();
    }, intervalMs);

    if (this.rotationInterval.unref) {
      this.rotationInterval.unref();
    }
  }

  private async rotateLogsByTime(): Promise<void> {
    const fileSize = this.getLogFileSize();

    if (fileSize === 0) {
      console.log("[Logger] No logs to rotate (file empty)");
      return;
    }

    if (this.isUploading) {
      console.log("[Logger] Upload in progress, skipping time-based rotation");
      return;
    }

    console.log(
      `[Logger] Time-based rotation triggered (${(fileSize / 1024 / 1024).toFixed(2)} MB)`
    );
    await this.uploadLog();
  }

  private stopAutoRotation(): void {
    if (this.rotationInterval) {
      clearInterval(this.rotationInterval);
      this.rotationInterval = null;
      console.log("[Logger] Auto-rotation stopped");
    }
  }

  private getLogFileSize(): number {
    try {
      const stats = statSync(this.logFilePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  private async uploadLog(): Promise<void> {
    if (this.isUploading) {
      console.log("[Logger] Upload already in progress, skipping...");
      return;
    }

    this.isUploading = true;

    try {
      const fileSize = this.getLogFileSize();
      if (fileSize === 0) {
        console.log("[Logger] Log file is empty, skipping upload");
        return;
      }

      console.log(
        `[Logger] Uploading to server: ${(fileSize / 1024 / 1024).toFixed(2)} MB`
      );

      await this.driver.upload(this.logFilePath, "app");
      this.deleteLogFile();

      console.log("[Logger] ✓ Log rotated: uploaded to server & local file deleted");
    } catch (error) {
      console.error("[Logger] ✗ Failed to rotate log file:", error);
    } finally {
      this.isUploading = false;
    }
  }

  private deleteLogFile(): void {
    try {
      unlinkSync(this.logFilePath);
      console.log("[Logger] Local log file deleted after upload");
    } catch (error) {
      console.error("[Logger] Failed to delete local log file:", error);
    }
  }

  private async checkAndUpload(): Promise<void> {
    const fileSize = this.getLogFileSize();

    if (fileSize >= this.maxLogSize && !this.isUploading) {
      console.log(
        `[Logger] Size limit exceeded (${(fileSize / 1024 / 1024).toFixed(2)} MB) - rotating logs...`
      );
      await this.uploadLog();
    }
  }

  /**
   * Static logging methods
   */
  static info(message: string, data?: any): void {
    const instance = PinoLogger.getInstance();
    instance.logger.info(data || {}, message);
    instance.checkAndUpload();
  }

  static error(message: string, error?: any): void {
    const instance = PinoLogger.getInstance();
    instance.logger.error({ err: error }, message);
    instance.checkAndUpload();
  }

  static warn(message: string, data?: any): void {
    const instance = PinoLogger.getInstance();
    instance.logger.warn(data || {}, message);
    instance.checkAndUpload();
  }

  static debug(message: string, data?: any): void {
    const instance = PinoLogger.getInstance();
    instance.logger.debug(data || {}, message);
    instance.checkAndUpload();
  }

  /**
   * Force upload logs
   */
  static async forceUpload(): Promise<void> {
    const instance = PinoLogger.getInstance();
    await instance.uploadLog();
  }

  /**
   * Graceful shutdown
   */
  static async shutdown(): Promise<void> {
    const instance = PinoLogger.getInstance();
    console.log("[Logger] Shutting down...");
    instance.stopAutoRotation();

    const fileSize = instance.getLogFileSize();
    if (fileSize > 0) {
      console.log("[Logger] Uploading remaining logs before shutdown...");
      await instance.uploadLog();
    }

    console.log("[Logger] Shutdown complete");
  }

  /**
   * Get the underlying Pino logger instance
   */
  static getLogger(): pino.Logger {
    const instance = PinoLogger.getInstance();
    return instance.logger;
  }
}

