import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import type { Metadata } from "next";
import "@repo/ui/globals.css";

import { Providers } from "@/components/providers";

import { SuperTokensProvider } from "@/components/auth";
import { Toaster } from "@repo/ui/components/sonner";

const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
});

const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
});

export const metadata: Metadata = {
  title: "Cadetlabs",
  description: "Generated by Pingcubes",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="h-full">
      <body className={`${fontSans.variable} ${fontMono.variable} h-full font-sans antialiased`}>
        <SuperTokensProvider>
          <Providers>{children}</Providers>
        </SuperTokensProvider>
        <Toaster position="top-center" />
      </body>
    </html>
  );
}
