import { Request, Response, Router } from 'express';
import { Service } from '../service/abstract.service';

export abstract class BaseController {

  abstract create(req: Request, res: Response): Promise<Response>;
  abstract get(req: Request, res: Response): Promise<Response>;
  abstract find(req: Request, res: Response): Promise<Response>;
  abstract update(req: Request, res: Response): Promise<Response>;
  abstract delete(req: Request, res: Response): Promise<Response>;

  protected sendResponse(res: Response, result: Service.Response<any>):void {
    if (result.success) {
       res.status(200).json(result.result);
    } else {
       res.status(result.error.status).json({ error: result.error.message });
    }
  }
}
