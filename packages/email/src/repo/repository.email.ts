import { BaseRepository } from "@repo/abstract";

export class EmailRepository extends BaseRepository<any> {
  constructor() {
    super("user");
  }

  public async findByEmail(email: string): Promise<any | null> {
    const client = this.getClient();
    return client.user.findUnique({
      where: { emailId: email },
      include: {
        tenant: true,
      },
    });
  }

  public async findById(id: string): Promise<any | null> {
    const client = this.getClient();
    return client.user.findUnique({
      where: { id },
      include: {
        tenant: true,
      },
    });
  }

  public async updateEmailVerificationStatus(id: string, isVerified: boolean): Promise<any> {
    const client = this.getClient();
    return client.user.update({
      where: { id },
      data: { emailVerified: isVerified },
    });
  }

  public async logEmailSent(data: {
    userId?: string;
    email: string;
    type: string;
    status: string;
    metadata?: any;
  }): Promise<any> {
    // This would be used to log email sending attempts
    // For now, we'll just return the data as we don't have an email log table
    return data;
  }
}
