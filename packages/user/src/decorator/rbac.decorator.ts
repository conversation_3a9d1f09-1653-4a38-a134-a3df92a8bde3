interface RbacConfig {
  resource: string;
  action: string;
}

/**
 * RBAC Decorator for controller methods
 * This decorator wraps the existing rbacMiddleware functionality
 *
 * @param resource - The resource being accessed (e.g., "user", "tenant")
 * @param action - The action being performed (e.g., "read", "create", "update", "delete")
 */
export function RequirePermission(resource: string, action: string): MethodDecorator {
  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (req: any, res: any, ...args: any[]) {
      try {
        // Check if metadata exists (should be set by metadataMiddleware)
        const metadata = req.metadata;
        if (!metadata) {
          return res.status(401).json({ error: "Unauthorized" });
        }

        const userRole = metadata.role;
        const userResources = metadata?.previlages?.resource || {};

        // Check if user has permissions for this resource
        const allowedActions = userResources[resource] || [];

        // "all" acts as a wildcard
        const isAllowed = allowedActions.includes("all") || allowedActions.includes(action);

        if (!isAllowed) {
          return res.status(403).json({
            error: "Forbidden",
            message: `Role '${userRole}' cannot ${action} on resource '${resource}'`,
          });
        }

        // Inject the RBAC decision into req for potential use in the method
        req.rbac = { resource, action, allowed: true };

        // Call the original method
        return await originalMethod.apply(this, [req, res, ...args]);
      } catch (err) {
        console.error("RBAC decorator error:", err);
        return res.status(500).json({ error: "Failed to check permissions" });
      }
    };

    // Store metadata for potential reflection
    Reflect.defineMetadata("rbac:config", { resource, action }, target, propertyKey);

    return descriptor;
  };
}

/**
 * Helper function to get RBAC metadata from a method
 */
export function getRbacMetadata(target: any, propertyKey: string | symbol): RbacConfig | undefined {
  return Reflect.getMetadata("rbac:config", target, propertyKey);
}

/**
 * Multiple permissions decorator - requires ALL permissions to be satisfied
 */
export function RequireAllPermissions(
  ...permissions: Array<{ resource: string; action: string }>
): MethodDecorator {
  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (req: any, res: any, ...args: any[]) {
      try {
        const metadata = req.metadata;
        if (!metadata) {
          return res.status(401).json({ error: "Unauthorized" });
        }

        const userRole = metadata.role;
        const userResources = metadata?.previlages?.resource || {};

        // Check all permissions
        for (const permission of permissions) {
          const allowedActions = userResources[permission.resource] || [];
          const isAllowed =
            allowedActions.includes("all") || allowedActions.includes(permission.action);

          if (!isAllowed) {
            return res.status(403).json({
              error: "Forbidden",
              message: `Role '${userRole}' cannot ${permission.action} on resource '${permission.resource}'`,
            });
          }
        }

        // Inject all RBAC decisions into req
        req.rbac = {
          permissions,
          allowed: true,
        };

        return await originalMethod.apply(this, [req, res, ...args]);
      } catch (err) {
        console.error("RBAC decorator error:", err);
        return res.status(500).json({ error: "Failed to check permissions" });
      }
    };

    Reflect.defineMetadata("rbac:permissions", permissions, target, propertyKey);
    return descriptor;
  };
}

/**
 * Multiple permissions decorator - requires ANY permission to be satisfied
 */
export function RequireAnyPermission(
  ...permissions: Array<{ resource: string; action: string }>
): MethodDecorator {
  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (req: any, res: any, ...args: any[]) {
      try {
        const metadata = req.metadata;
        if (!metadata) {
          return res.status(401).json({ error: "Unauthorized" });
        }

        const userRole = metadata.role;
        const userResources = metadata?.previlages?.resource || {};

        // Check if user has ANY of the required permissions
        let hasPermission = false;
        let satisfiedPermission: { resource: string; action: string } | null = null;

        for (const permission of permissions) {
          const allowedActions = userResources[permission.resource] || [];
          const isAllowed =
            allowedActions.includes("all") || allowedActions.includes(permission.action);

          if (isAllowed) {
            hasPermission = true;
            satisfiedPermission = permission;
            break;
          }
        }

        if (!hasPermission) {
          const permissionStrings = permissions
            .map((p) => `${p.action} on ${p.resource}`)
            .join(" OR ");
          return res.status(403).json({
            error: "Forbidden",
            message: `Role '${userRole}' requires one of: ${permissionStrings}`,
          });
        }

        req.rbac = {
          permissions,
          satisfiedPermission,
          allowed: true,
        };

        return await originalMethod.apply(this, [req, res, ...args]);
      } catch (err) {
        console.error("RBAC decorator error:", err);
        return res.status(500).json({ error: "Failed to check permissions" });
      }
    };

    Reflect.defineMetadata("rbac:any-permissions", permissions, target, propertyKey);
    return descriptor;
  };
}
