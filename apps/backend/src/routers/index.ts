// routers/index.ts
import { type Express } from "express";
import { metadataMiddleware, AuthService } from "@repo/supertoken";
import { AuthRoute } from "./router.auth";
import { TenantRoute } from "./router.tenant";
// import { UserRoute } from "./router.user";
export function initRouters(app: Express): void {
  app.use("/auth", AuthRoute.getRouter());
  // app.use("/api/users", AuthService.verifySession(), metadataMiddleware(), UserRoute.getRouter());
  app.use(
    "/api/tenant",
    AuthService.verifySession(),
    metadataMiddleware(),
    TenantRoute.getRouter()
  );

  app.get("/health", (_req, res) => {
    res.json({
      success: true,
      message: "Backend service is healthy",
      timestamp: new Date().toISOString(),
    });
  });

  console.log("✓ Routers initialized");
}
