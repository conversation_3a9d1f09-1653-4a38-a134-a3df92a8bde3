import { BaseRepository } from "@repo/abstract";

export class AuthRepository extends BaseRepository<any> {
  constructor() {
    super("user");
  }

  public async findByEmail(email: string): Promise<any | null> {
    const client = this.getClient();
    return client.user.findUnique({
      where: { emailId: email },
      include: {
        tenant: true,
      },
    });
  }

  public async findById(id: string): Promise<any | null> {
    const client = this.getClient();
    return client.user.findUnique({
      where: { id },
      include: {
        tenant: true,
      },
    });
  }

  public async createUser(userData: any): Promise<any> {
    const client = this.getClient();
    return client.user.create({
      data: userData,
      include: {
        tenant: true,
      },
    });
  }

  public async updateUser(id: string, userData: any): Promise<any> {
    const client = this.getClient();
    return client.user.update({
      where: { id },
      data: userData,
      include: {
        tenant: true,
      },
    });
  }

  public async updateLastLogin(id: string): Promise<any> {
    const client = this.getClient();
    return client.user.update({
      where: { id },
      data: { lastLoginAt: new Date() },
    });
  }
}
