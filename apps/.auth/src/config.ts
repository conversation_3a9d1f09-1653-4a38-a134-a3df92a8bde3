import dotenv from "dotenv";
dotenv.config();

function getEnvVar(key: string, required = true, fallback?: string): string {
  const value = process.env[key] ?? fallback;
  if (required && (value === undefined || value === "")) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
  return value as string;
}

export const config = {
  SUPERTOKENS_CONNECTION_URI: getEnvVar("SUPERTOKENS_CONNECTION_URI"),
  SUPERTOKENS_API_DOMAIN: getEnvVar("SUPERTOKENS_API_DOMAIN"),
  SUPERTOKENS_WEBSITE_DOMAIN: getEnvVar("SUPERTOKENS_WEBSITE_DOMAIN"),
  AZURE_COMMUNICATION_CONNECTION_STRING: getEnvVar("AZURE_COMMUNICATION_CONNECTION_STRING"),
  PORT: parseInt(getEnvVar("PORT", false, "3005"), 10),
  database: {
    user: getEnvVar("POSTGRESQL_USER", false, "azure_root"),
    password: getEnvVar("POSTGRESQL_PASSWORD", false, "Zeref@2111"),
    host: getEnvVar("POSTGRESQL_HOST", false, "testserver96.postgres.database.azure.com"),
    port: parseInt(getEnvVar("POSTGRESQL_PORT", false, "5432"), 10),
    database: getEnvVar("POSTGRESQL_DATABASE_NAME", false, "postgres"),
    schema: getEnvVar("POSTGRESQL_TABLE_SCHEMA", false, "app"),
  }
};