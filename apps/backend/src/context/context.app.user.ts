import { AsyncLocalStorage } from 'async_hooks';
import { Request, Response, NextFunction } from 'express';

interface ContextData {
  [key: string]: any;
}

interface MiddlewareOptions {
  getUserInfo?: (req: Request) => ContextData;
}

export class AsyncContext {
  private storage: AsyncLocalStorage<ContextData>;
    private static ctx:AsyncContext;
  private constructor() {
    this.storage = new AsyncLocalStorage<ContextData>();
  }

  
  public run(data: ContextData, callback: () => void | Promise<void>): void {
    this.storage.run(data, callback);
  }


  public get(): ContextData | undefined {
    return this.storage.getStore();
  }

 
  public getKey(key: string): any {
    const store = this.storage.getStore();
    return store ? store[key] : undefined;
  }


  public set(key: string, value: any): void {
    const store = this.storage.getStore();
    if (store) {
      store[key] = value;
    }
  }

  public has(): boolean {
    return this.storage.getStore() !== undefined;
  }


  public middleware(options: MiddlewareOptions = {}) {
    const { getUserInfo = () => ({}) } = options;

    return (req: Request, res: Response, next: NextFunction): void => {
      const contextData: ContextData = {
        requestId: Math.random().toString(36).substring(7),
        timestamp: Date.now(),
        ...getUserInfo(req),
      };

      this.storage.run(contextData, () => {
        next();
      });
    };
  }

  public static getInstance():AsyncContext{
    if(!AsyncContext.ctx)
        AsyncContext.ctx = new AsyncContext()
    return AsyncContext.ctx;
  }

}