import { PrismaClient } from "../generated/client";
import { AppConfig as config } from "@repo/config";

export interface DatabaseConfig {
  user: string;
  password: string;
  host: string;
  port: number;
  database: string;
  schema?: string;
}

export class Database {
  private static prisma: PrismaClient | null = null;
  private static isConnected: boolean = false;

  private static buildConnectionString(config: DatabaseConfig): string {
    const schema = config.schema || "public";
    return `postgresql://${encodeURIComponent(config.user)}:${encodeURIComponent(
      config.password
    )}@${config.host}:${config.port}/${config.database}?schema=${schema}`;
  }

  private static async initialize(config: DatabaseConfig): Promise<void> {
    if (Database.isConnected && Database.prisma) return;

    const url = Database.buildConnectionString(config);

    Database.prisma = new PrismaClient({
      datasources: {
        db: { url },
      },
      log: ["error", "warn"],
    });

    await Database.prisma.$connect();
    Database.isConnected = true;
  }

  private static async initializeFromEnv(): Promise<void> {
    console.log("Initializing database from env");
    console.log(config.getConfig());
    await Database.initialize({
      user: config.getConfig().POSTGRESQL_USER,
      password: config.getConfig().POSTGRESQL_PASSWORD,
      host: config.getConfig().POSTGRESQL_HOST,
      port: config.getConfig().POSTGRESQL_PORT,
      database: config.getConfig().POSTGRESQL_DATABASE_NAME,
      schema: config.getConfig().POSTGRESQL_TABLE_SCHEMA,
    });
  }

  public static async getClient(): Promise<PrismaClient> {
    if (!Database.prisma || !Database.isConnected) {
      await Database.initializeFromEnv();
    }
    return Database.prisma!;
  }

  public static isReady(): boolean {
    return Database.isConnected && Database.prisma !== null;
  }

  public static async disconnect(): Promise<void> {
    if (!Database.prisma) return;
    await Database.prisma.$disconnect();
    Database.isConnected = false;
    Database.prisma = null;
  }
}
