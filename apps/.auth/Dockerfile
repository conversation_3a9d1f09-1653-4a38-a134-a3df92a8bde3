# -----------------------
# Base Alpine image
# -----------------------
ARG NODE_VERSION=18.18.0
FROM node:${NODE_VERSION}-alpine AS alpine
RUN apk update && apk add --no-cache libc6-compat

# -----------------------
# Base image with pnpm
# -----------------------
FROM alpine AS base
RUN npm install -g pnpm
RUN pnpm config set store-dir ~/.pnpm-store

# -----------------------
# Pruner stage
# -----------------------
FROM base AS pruner
ARG PROJECT=auth
WORKDIR /app

# Copy all monorepo files needed for pruning
COPY package*.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY apps ./apps
COPY packages ./packages

# Install dependencies locally to have turbo available
RUN pnpm install 

# Run Turbo prune to isolate only the auth project and its dependencies
RUN pnpm turbo prune --scope=${PROJECT} --docker

# -----------------------
# Builder stage
# -----------------------
FROM base AS builder
ARG PROJECT=auth
WORKDIR /app

# Copy lockfile and workspace config from pruner
COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=pruner /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml

# Copy pruned package.json's for dependencies
COPY --from=pruner /app/out/json/ .

# Install dependencies for the pruned project
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --frozen-lockfile

# Copy pruned source code from pruner
COPY --from=pruner /app/out/full/ .

# Copy turbo.json from the **build context** (not from pruner)
COPY turbo.json ./turbo.json

# Build the project (from monorepo root)
RUN pnpm turbo build --filter=${PROJECT}

# Prune dev dependencies and remove source code
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm prune --prod --no-optional
RUN rm -rf ./**/*/src

# -----------------------
# Production runner
# -----------------------
FROM alpine AS runner
ARG PROJECT=auth

# Create non-root user
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nodejs
USER nodejs

WORKDIR /app

# Copy built project
COPY --from=builder --chown=nodejs:nodejs /app .

WORKDIR /app/apps/${PROJECT}

# Environment
ARG PORT=3005
ENV PORT=${PORT}
ENV NODE_ENV=production
EXPOSE ${PORT}

# Start the app (adjust if your auth app uses start script instead of compiled dist)
CMD ["node", "dist/index.cjs"]
