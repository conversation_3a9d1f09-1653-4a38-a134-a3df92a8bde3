// Simple test script to trigger email verification
import axios from "axios";

async function testEmailVerification() {
  try {
    console.log("🧪 Testing email verification...");

    // Test sending email verification
    const response = await axios.post("http://localhost:3005/auth/user/email/verify/send", {
      email: "<EMAIL>", // Replace with your email
    });

    console.log("✅ Response:", response.data);
    console.log("📧 Check your email and server logs for verification link");
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);
  }
}

testEmailVerification();
