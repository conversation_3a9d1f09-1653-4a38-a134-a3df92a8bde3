import { BaseService, Service, ServiceError } from "@repo/abstract";
import { EmailRepository } from "../repo/repository.email";
import EmailVerification from "supertokens-node/recipe/emailverification";
import { listUsersByAccountInfo, User } from "supertokens-node";
import supertokens from "supertokens-node";
import { EmailClient } from "@azure/communication-email";
import * as fs from "fs/promises";
import * as path from "path";

const TENANT_ID = process.env.SUPERTOKENS_TENANT_ID || "public";
const WEBSITE_DOMAIN = process.env.WEBSITE_DOMAIN || "http://localhost:3000";

interface SendEmailVerificationPayload {
  email: string;
}

interface VerifyEmailTokenPayload {
  token: string;
}

export class EmailService extends BaseService<any, any> {
  private emailRepository: EmailRepository;
  private static emailClient: EmailClient;

  constructor() {
    super("EmailService");
    this.emailRepository = new EmailRepository();
  }

  // Initialize the email client (call this once in your application startup)
  static initialize(connectionString: string) {
    this.emailClient = new EmailClient(connectionString);
  }

  public async sendEmailVerification(params: Service.Params<SendEmailVerificationPayload>) {
    return this.execute(params, async (params) => {
      const { payload } = params;
      const { email } = payload;

      if (!email) {
        throw new ServiceError("Email is required", "VALIDATION_ERROR", 400);
      }

      try {
        const users: User[] = await listUsersByAccountInfo(TENANT_ID, { email });
        if (!users || users.length === 0) {
          // Return success even if user doesn't exist for security
          return {
            status: "OK",
            note: "If the email exists, a verification message will be sent.",
          };
        }

        const user = users[0];
        let recipeUserId;
        if (user.loginMethods && user.loginMethods.length > 0 && user.loginMethods[0].recipeUserId) {
          recipeUserId = user.loginMethods[0].recipeUserId;
        } else {
          recipeUserId = new supertokens.RecipeUserId(user.id);
        }

        const tokenResp = await EmailVerification.createEmailVerificationToken(
          TENANT_ID,
          recipeUserId,
          email
        );

        if (tokenResp.status !== "OK") {
          throw new ServiceError("Failed to create verification token", "TOKEN_ERROR", 500);
        }

        const token = tokenResp.token;
        const verificationLink = `${WEBSITE_DOMAIN}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;

        await this.sendEmailWithTemplate(
          {
            to: email,
            subject: "Please verify your email",
            data: {
              appName: "CadetLabs",
              verificationLink,
            },
          },
          "email-verify"
        );

        return { status: "OK" };
      } catch (error: any) {
        if (error instanceof ServiceError) {
          throw error;
        }
        throw new ServiceError("Failed to send verification email", "EMAIL_SEND_ERROR", 500, error);
      }
    });
  }

  public async verifyEmailToken(params: Service.Params<VerifyEmailTokenPayload>) {
    return this.execute(params, async (params) => {
      const { payload } = params;
      const { token } = payload;

      if (!token) {
        throw new ServiceError("Token is required", "VALIDATION_ERROR", 400);
      }

      try {
        const response = await EmailVerification.verifyEmailUsingToken(TENANT_ID, token);

        if (response.status === "OK") {
          return {
            status: "OK",
            message: "Email verified successfully",
            user: response.user,
          };
        } else if (response.status === "EMAIL_VERIFICATION_INVALID_TOKEN_ERROR") {
          throw new ServiceError("Invalid or expired token", "INVALID_TOKEN", 400);
        }

        throw new ServiceError("Email verification failed", "VERIFICATION_ERROR", 500);
      } catch (error: any) {
        if (error instanceof ServiceError) {
          throw error;
        }
        throw new ServiceError("Email verification failed", "VERIFICATION_ERROR", 500, error);
      }
    });
  }

  public async sendEmailVerificationToUserByEmail(email: string) {
    const users: User[] = await listUsersByAccountInfo(TENANT_ID, { email });
    if (!users || users.length === 0) {
      return;
    }
    const user = users[0];
    let recipeUserId;
    if (user.loginMethods && user.loginMethods.length > 0 && user.loginMethods[0].recipeUserId) {
      recipeUserId = user.loginMethods[0].recipeUserId;
    } else {
      recipeUserId = new supertokens.RecipeUserId(user.id);
    }
    const tokenResp = await EmailVerification.createEmailVerificationToken(
      TENANT_ID,
      recipeUserId,
      email
    );
    if (tokenResp.status !== "OK") {
      return;
    }

    const token = tokenResp.token;
    const verificationLink = `${WEBSITE_DOMAIN}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;
    
    await this.sendEmailWithTemplate(
      {
        to: email,
        subject: "Please verify your email",
        data: {
          appName: "CadetLabs",
          verificationLink,
        },
      },
      "email-verify"
    );
  }

  private async sendEmailWithTemplate(
    content: { to: string; subject: string; data: object },
    template: string
  ) {
    try {
      // Ensure email client is initialized
      if (!EmailService.emailClient) {
        throw new Error("EmailClient not initialized. Call EmailService.initialize() first.");
      }

      // Default template path
      const templatePath = `src/supertoken/${template}.html`;

      // Read the HTML template
      const htmlTemplate = await this.readTemplate(templatePath);

      // Replace placeholders in the template with actual data
      const htmlContent = this.replacePlaceholders(htmlTemplate, content.data);

      // Prepare the email message
      const emailMessage = {
        senderAddress: process.env.SENDER_EMAIL || "<EMAIL>",
        content: {
          subject: content.subject,
          html: htmlContent,
        },
        recipients: {
          to: [
            {
              address: content.to,
            },
          ],
        },
      };

      // Send the email
      const poller = await EmailService.emailClient.beginSend(emailMessage);
      const result = await poller.pollUntilDone();

      console.log(`Email sent successfully. Message ID: ${result.id}`);
      return result;
    } catch (error) {
      console.error("Error sending email:", error);
      throw error;
    }
  }

  // Helper method to read template file
  private async readTemplate(templatePath: string): Promise<string> {
    try {
      const absolutePath = path.resolve(templatePath);
      const template = await fs.readFile(absolutePath, "utf-8");
      return template;
    } catch (error) {
      console.error(`Error reading template file: ${templatePath}`, error);
      throw new Error(`Failed to read email template: ${templatePath}`);
    }
  }

  // Helper method to replace placeholders in template
  private replacePlaceholders(template: string, data: any): string {
    let result = template;
    for (const [key, value] of Object.entries(data)) {
      const placeholder = new RegExp(`{{${key}}}`, "g");
      result = result.replace(placeholder, String(value));
    }
    return result;
  }
}
