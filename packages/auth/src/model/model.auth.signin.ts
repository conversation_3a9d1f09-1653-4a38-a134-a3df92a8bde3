import { Is<PERSON><PERSON>, Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from "@repo/model-transformer";

export class SignInDto {
  @IsArray()
  @IsNotEmpty({ message: "Form fields are required" })
  formFields!: Array<{
    id: string;
    value: string;
  }>;
}

export class SignInFieldsDto {
  @IsEmail({}, { message: "Email must be valid" })
  @IsNotEmpty({ message: "Email is required" })
  email!: string;

  @IsString()
  @IsNotEmpty({ message: "Password is required" })
  @Length(6, 100, { message: "Password must be between 6 and 100 characters" })
  password!: string;
}
