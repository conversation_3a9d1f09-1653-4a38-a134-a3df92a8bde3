import { Router } from "express";
import { verifySession } from "@repo/supertoken";
import { TenantController } from "@repo/tenant";
export class TenantRoute {
  private static route = Router();
  private static init() {
    const tenantController = new TenantController();

    // TenantRoute.route.post("/", TenantController.create);
    // TenantRoute.route.get("/:id", TenantController.get);
    // TenantRoute.route.get("/", TenantController.find);
    // TenantRoute.route.put("/:id", TenantController.update);
    // TenantRoute.route.delete("/:id", TenantController.delete);
    // TenantRoute.route.get("/:tenantId/users", TenantController.getUsersByTenant);
    TenantRoute.route.post("/list", tenantController.listTenants.bind(tenantController));
  }

  static getRouter(): Router {
    TenantRoute.init();
    return TenantRoute.route;
  }
}
