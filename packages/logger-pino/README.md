# @repo/logger-pino

A production-ready Pino logger with automatic log rotation and cloud storage upload capabilities.

## Features

- 🚀 **High Performance**: Built on Pino, one of the fastest Node.js loggers
- 🔄 **Automatic Rotation**: Time-based and size-based log rotation
- ☁️ **Cloud Storage**: Upload logs to Azure Blob Storage, S3, or local backup
- 🎯 **Express Middleware**: Built-in request/response logging
- 🔧 **<PERSON><PERSON>**: Easy to use across your application
- 📊 **Multiple Log Levels**: info, error, warn, debug
- 🛡️ **Graceful Shutdown**: Ensures logs are uploaded before exit

## Installation

This package is part of the monorepo and is installed automatically via workspace dependencies.

```json
{
  "dependencies": {
    "@repo/logger-pino": "workspace:*"
  }
}
```

## Usage

### 1. Initialize the Logger

Initialize the logger once at application startup:

```typescript
import { PinoLogger, LocalDriver, AzureBlobDriver } from '@repo/logger-pino';

// For development (local backup)
const localDriver = new LocalDriver('./logs/backup');
PinoLogger.initialize({
  driver: localDriver,
  logDirectory: './logs',
  maxLogSizeMB: 10,
  level: 'debug',
  rotationIntervalMinutes: 5,
});

// For production (Azure Blob Storage)
const azureDriver = AzureBlobDriver.initialize({
  connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING!,
  containerName: 'application-logs',
});
PinoLogger.initialize({
  driver: azureDriver,
  logDirectory: './logs',
  maxLogSizeMB: 10,
  level: 'info',
  rotationIntervalMinutes: 5,
});
```

### 2. Use as Express Middleware

Add the logger middleware to automatically log all HTTP requests and responses:

```typescript
import express from 'express';
import { PinoLogger } from '@repo/logger-pino';

const app = express();
app.use(PinoLogger.middleware());
```

### 3. Log Messages in Your Code

Use the static methods to log messages anywhere in your application:

```typescript
import { PinoLogger } from '@repo/logger-pino';

// Info logging
PinoLogger.info('User logged in', { userId: 123, email: '<EMAIL>' });

// Error logging
try {
  // some operation
} catch (error) {
  PinoLogger.error('Operation failed', error);
}

// Warning logging
PinoLogger.warn('Rate limit approaching', { userId: 123, requests: 95 });

// Debug logging
PinoLogger.debug('Processing request', { requestId: 'abc-123' });
```

### 4. Graceful Shutdown

Ensure logs are uploaded before the application exits:

```typescript
process.on('SIGINT', async () => {
  await PinoLogger.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await PinoLogger.shutdown();
  process.exit(0);
});
```

## Configuration Options

### LoggerConfig

| Option | Type | Required | Default | Description |
|--------|------|----------|---------|-------------|
| `driver` | `UploadDriver` | Yes | - | Storage driver for log uploads |
| `logDirectory` | `string` | Yes | - | Local directory for log files |
| `maxLogSizeMB` | `number` | Yes | - | Maximum log file size before rotation |
| `level` | `string` | No | `'info'` | Minimum log level (debug, info, warn, error) |
| `rotationIntervalMinutes` | `number` | No | `5` | Time interval for automatic rotation |

## Available Drivers

### LocalDriver

Backs up logs to a local directory (useful for development):

```typescript
import { LocalDriver } from '@repo/logger-pino';

const driver = new LocalDriver('./logs/backup');
```

### AzureBlobDriver

Uploads logs to Azure Blob Storage:

```typescript
import { AzureBlobDriver } from '@repo/logger-pino';

const driver = AzureBlobDriver.initialize({
  connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING!,
  containerName: 'application-logs',
});
```

### S3Driver

Placeholder for AWS S3 uploads (to be implemented):

```typescript
import { S3Driver } from '@repo/logger-pino';

const driver = new S3Driver();
```

## Custom Driver

You can create your own storage driver by implementing the `UploadDriver` interface:

```typescript
import { UploadDriver } from '@repo/logger-pino';

class CustomDriver implements UploadDriver {
  async upload(filePath: string, fileName: string): Promise<void> {
    // Your custom upload logic
  }
}
```

## Log Rotation

The logger automatically rotates logs based on:

1. **Size**: When the log file exceeds `maxLogSizeMB`
2. **Time**: Every `rotationIntervalMinutes` minutes

When rotation occurs:
1. The current log file is uploaded to the configured storage
2. The local log file is deleted
3. A new log file is created

## Best Practices

1. **Initialize Once**: Call `PinoLogger.initialize()` only once at application startup
2. **Use Appropriate Levels**: 
   - `debug`: Detailed information for debugging
   - `info`: General informational messages
   - `warn`: Warning messages for potentially harmful situations
   - `error`: Error messages for serious problems
3. **Include Context**: Always include relevant data with your log messages
4. **Graceful Shutdown**: Always call `shutdown()` before exiting
5. **Environment-Based Config**: Use different drivers for dev/prod environments

## Example: Complete Setup

```typescript
import express from 'express';
import { PinoLogger, LocalDriver, AzureBlobDriver } from '@repo/logger-pino';

// Initialize logger
const isDev = process.env.NODE_ENV === 'development';
const driver = isDev 
  ? new LocalDriver('./logs/backup')
  : AzureBlobDriver.initialize({
      connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING!,
      containerName: 'application-logs',
    });

PinoLogger.initialize({
  driver,
  logDirectory: './logs',
  maxLogSizeMB: 10,
  level: isDev ? 'debug' : 'info',
  rotationIntervalMinutes: 5,
});

// Setup Express app
const app = express();
app.use(PinoLogger.middleware());

// Use in routes
app.get('/api/users', (req, res) => {
  PinoLogger.info('Fetching users');
  res.json({ users: [] });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await PinoLogger.shutdown();
  process.exit(0);
});

app.listen(3000, () => {
  PinoLogger.info('Server started', { port: 3000 });
});
```

## License

Private package for internal use.

