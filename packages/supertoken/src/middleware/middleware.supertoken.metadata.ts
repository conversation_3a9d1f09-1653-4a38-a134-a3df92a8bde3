import UserMetadata from "supertokens-node/recipe/usermetadata";
import { NextFunction, Request, Response } from "express";
export function metadataMiddleware() {
    return async (req: Request & any, res: Response, next: NextFunction) => {
        try {
            const userId = req.session!.getUserId();
            const metadata = await UserMetadata.getUserMetadata(userId);
            req.metadata = metadata.metadata;
            next();
        } catch (err) {
            console.error("metadata middleware error:", err);
            res.status(500).json({ error: "Failed to fetch metadata" });
        }
    }
};