version: "3.7"

services:
  supertokens:
    image: supertokens/supertokens-postgresql
    container_name: supertokens
    restart: unless-stopped
    ports:
      - "3567:3567"
    environment:
      NODE_ENV: production
      POSTGRESQL_USER: dev_db_pg_owner
      POSTGRESQL_PASSWORD: Ce3Y5YqA78qGH@E_cadet_root
      POSTGRESQL_HOST: cl-pab-dev-sindia-db-1.postgres.database.azure.com
      POSTGRESQL_PORT: 5432
      POSTGRESQL_DATABASE_NAME: dev
      POSTGRESQL_TABLE_SCHEMA: auth
      AZURE_COMMUNICATION_CONNECTION_STRING: "endpoint=https://cl-test-email.india.communication.azure.com/;accesskey=3H4xEGF5Vuwk2bFETGT7fieIjOMaV761f7hdY9Fj7g7piTeJd2VTJQQJ99BIACULyCp3zDT8AAAAAZCShL26"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3567/"]
      interval: 30s
      timeout: 10s
      retries: 5

  auth-backend:
    build:
      context: .
      dockerfile: apps/auth/Dockerfile
    container_name: auth-backend
    restart: unless-stopped
    ports:
      - "3005:3005"
    environment:
      NODE_ENV: production
      SUPERTOKENS_CONNECTION_URI: http://supertokens:3567
      POSTGRESQL_USER: dev_db_pg_owner
      POSTGRESQL_PASSWORD: Ce3Y5YqA78qGH@E_cadet_root
      POSTGRESQL_HOST: cl-pab-dev-sindia-db-1.postgres.database.azure.com
      POSTGRESQL_PORT: 5432
      POSTGRESQL_DATABASE_NAME: dev
      POSTGRESQL_TABLE_SCHEMA: app
      AZURE_COMMUNICATION_CONNECTION_STRING: "endpoint=https://cl-test-email.india.communication.azure.com/;accesskey=3H4xEGF5Vuwk2bFETGT7fieIjOMaV761f7hdY9Fj7g7piTeJd2VTJQQJ99BIACULyCp3zDT8AAAAAZCShL26"

    depends_on:
      supertokens:
        condition: service_healthy

networks:
  app-network:
    driver: bridge
