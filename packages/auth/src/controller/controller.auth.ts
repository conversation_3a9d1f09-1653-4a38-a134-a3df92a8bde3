import "reflect-metadata";

import { <PERSON><PERSON>ontroller } from "@repo/abstract";
import { type Request, type Response, Router } from "express";
import { AuthService } from "../service/service.auth";
import { Specification } from "@repo/model-transformer";
import { SignInDto } from "../model/model.auth.signin";
import { SignUpDto } from "../model/model.auth.signup";

export class AuthController extends BaseController {
  private readonly router: Router;
  private service: AuthService;

  constructor() {
    super();
    this.router = Router();
    this.service = new AuthService();
  }

  @Specification({ param_blueprint: SignInDto }, { validate: true })
  async signIn(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.signIn({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: { ...req.body, req, res },
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ status: error.code || "ERROR", message: error.message });
    }
  }

  @Specification({ param_blueprint: SignUpDto }, { validate: true })
  async signUp(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.signUp({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: req.body,
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ status: error.code || "ERROR", message: error.message });
    }
  }

  async signOut(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.signOut({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: { session: req.session },
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  // Standard CRUD methods (not used for auth but required by BaseController)
  override async create(req: Request, res: Response): Promise<Response> {
    return this.signUp(req, res);
  }

  override async get(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  override async find(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  override async update(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  override async delete(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  getRouter(): Router {
    // Authentication routes
    this.router.post("/signin", this.signIn.bind(this));
    this.router.post("/signup", this.signUp.bind(this));
    this.router.post("/signout", this.signOut.bind(this));

    return this.router;
  }
}
