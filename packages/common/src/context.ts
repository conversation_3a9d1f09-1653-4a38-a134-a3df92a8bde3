import { AsyncLocalStorage } from "async_hooks";
export interface Context {

  readonly user: Record<string, any>;
}

const storage = new AsyncLocalStorage<Context>();

export class ApplicationContext {
  static run<T>(ctx: Context, callback: () => T) {
    return storage.run(ctx, callback);
  }

  static get(): Context | undefined {
    return storage.getStore();
  }

  static require(): Context {
    const ctx = storage.getStore();
    if (!ctx) throw new Error("No context found. Did you forget to add contextMiddleware?");
    return ctx;
  }

  static set<K extends keyof Context>(key: K, value: Context[K]) {
    const store = storage.getStore();
    if (store) {
      (store as any)[key] = value;
    }
  }
}