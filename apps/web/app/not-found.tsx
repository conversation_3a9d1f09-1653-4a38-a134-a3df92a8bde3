"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import logo from "../public/logo.png";

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <Card className="text-center shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-6">
            {/* Logo */}
            <div className="flex justify-center mb-6">
              <div className="bg-primary/10 p-4 rounded-2xl">
                <Image src={logo} alt="Cadetlabs" className="h-12 w-14 object-contain" />
              </div>
            </div>

            {/* 404 Illustration */}
            <div className="relative mb-6">
              <div className="flex items-center justify-center space-x-4">
                {/* Large 404 Text with decorative elements */}
                <div className="relative">
                  <div className="text-8xl font-bold text-primary/20 select-none">4</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.fileQuestion className="h-12 w-12 text-primary" />
                  </div>
                </div>
                <div className="text-8xl font-bold text-primary/20 select-none">0</div>
                <div className="relative">
                  <div className="text-8xl font-bold text-primary/20 select-none">4</div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Icons.search className="h-12 w-12 text-primary" />
                  </div>
                </div>
              </div>
              
              {/* Floating decorative elements */}
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary/10 rounded-full animate-pulse"></div>
              <div className="absolute -top-2 -right-6 w-6 h-6 bg-primary/20 rounded-full animate-pulse delay-300"></div>
              <div className="absolute -bottom-2 left-8 w-4 h-4 bg-primary/15 rounded-full animate-pulse delay-700"></div>
            </div>

            {/* Error Message */}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-foreground">Page Not Found</h1>
              <p className="text-lg text-muted-foreground max-w-md mx-auto">
                Oops! The page you're looking for seems to have sailed away. 
                Let's get you back on course.
              </p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Helpful suggestions */}
            <div className="bg-muted/50 rounded-lg p-4 text-left">
              <h3 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                <Icons.AlertTriangle className="h-4 w-4 text-primary" />
                What you can do:
              </h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                  Check the URL for any typos
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                  Go back to the previous page
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                  Visit our homepage to start fresh
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button 
                onClick={() => window.history.back()} 
                variant="outline" 
                className="flex items-center gap-2"
              >
                <Icons.arrowLeft className="h-4 w-4" />
                Go Back
              </Button>
              
              <Link href="/dashboard">
                <Button className="flex items-center gap-2 w-full sm:w-auto">
                  <Icons.home className="h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>

            {/* Contact Support */}
            <div className="pt-4 border-t border-border/50">
              <p className="text-sm text-muted-foreground">
                Still having trouble?{" "}
                <Link href="/help" className="text-primary hover:text-primary/80 font-medium">
                  Contact Support
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
