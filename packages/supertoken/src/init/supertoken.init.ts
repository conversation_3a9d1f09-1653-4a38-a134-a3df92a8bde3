import supertokens from "supertokens-node";
import EmailVerification from "supertokens-node/recipe/emailverification";
import Session from "supertokens-node/recipe/session";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import { middleware, errorHandler } from "supertokens-node/framework/express";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { verifySession } from "supertokens-node/recipe/session/framework/express";
export interface authConfig {
  connectionURI: string;
  appInfo: {
    appName: string;
    apiDomain: string;
    websiteDomain: string;
    apiBasePath?: string;
    websiteBasePath?: string;
  };
}

export class AuthService {
  private config: authConfig;
  private initialized = false;

  constructor(config: authConfig) {
    this.config = config;
  }

  init(): void {
    if (this.initialized) return; // prevent duplicate init

    supertokens.init({
      framework: "express",
      supertokens: {
        connectionURI: this.config.connectionURI,
      },
      appInfo: this.config.appInfo,
      recipeList: [
        EmailVerification.init({
          mode: "REQUIRED",
        }),
        EmailPassword.init({
          override: {
            apis: (originalImplementation) => ({
              ...originalImplementation,
              signInPOST: async (input) => {
                // Add custom checks here (user active, etc.)
                return await originalImplementation.signInPOST(input);
              },
              signUpPOST: undefined, // disable sign up
            }),
          },
        }),
        UserMetadata.init(),
        Session.init({
          cookieSecure: process.env.NODE_ENV === "production",
          cookieSameSite: "lax",
          exposeAccessTokenToFrontendInCookieBasedAuth: true,
        }),
      ],
    });

    this.initialized = true;
  }

  getMiddleware() {
    if (!this.initialized) {
      throw new Error("SuperTokens not initialized. Call init() first.");
    }
    return middleware();
  }

  getErrorHandler() {
    if (!this.initialized) {
      throw new Error("SuperTokens not initialized. Call init() first.");
    }
    return errorHandler();
  }
  static verifySession() {
    return verifySession();
  }
}

export function createDefaultSuperTokensService(): AuthService {
  return new AuthService({
    connectionURI: process.env.SUPERTOKENS_CONNECTION_URI || "http://localhost:3567",
    appInfo: {
      appName: "CadetLabs",
      apiDomain: process.env.API_DOMAIN || "http://localhost:3005",
      websiteDomain: process.env.WEBSITE_DOMAIN || "http://localhost:3000",
      apiBasePath: "/auth",
      websiteBasePath: "/auth",
    },
  });
}
export { middleware, errorHandler } from "supertokens-node/framework/express";
