{"name": "@repo/database", "version": "0.0.0", "private": true, "files": ["dist", "prisma", "generated"], "exports": {"./client": {"types": "./generated/client/index.d.ts", "import": "./generated/client/index.js", "require": "./generated/client/index.js"}, ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "bunchee", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.20.0", "pg": "^8.16.3", "prisma": "^5.20.0"}, "devDependencies": {"@repo/config": "workspace:*", "@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/pg": "^8.15.5", "eslint": "^9.31.0", "tsup": "^8.5.0", "bunchee": "^6.4.0", "typescript": "5.8.2"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "tsx prisma/seed.ts"}}