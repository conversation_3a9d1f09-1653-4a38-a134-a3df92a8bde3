import { NextFunction, Response, Request } from "express";
import { AsyncContext } from "../context/context.app.user";
interface ContextData {
  [key: string]: any;
}

interface MiddlewareOptions {
  getUserInfo?: (req: Request) => ContextData;
}

export function contextMiddleware(options: MiddlewareOptions = {}) {
  const { getUserInfo = () => ({}) } = options;

  return (req: Request, res: Response, next: NextFunction): void | Promise<void> => {
    const contextData = {
      requestId: Math.random().toString(36).substring(7),
      timestamp: Date.now(),
      ...getUserInfo(req),
    };

    AsyncContext.getInstance().run(contextData, () => {
      next();
    });
  };
}