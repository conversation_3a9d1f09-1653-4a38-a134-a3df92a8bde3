import { BaseService, Service, ServiceError } from "@repo/abstract";
import { AuthRepository } from "../repo/repository.auth";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import Session from "supertokens-node/recipe/session";
import UserMetadata from "supertokens-node/recipe/usermetadata";

interface SignInPayload {
  formFields: Array<{
    id: string;
    value: string;
  }>;
}

interface SignUpPayload {
  formFields: Array<{
    id: string;
    value: string;
  }>;
}

export class AuthService extends BaseService<any, any> {
  private authRepository: AuthRepository;

  constructor() {
    super("AuthService");
    this.authRepository = new AuthRepository();
  }

  public async signIn(params: Service.Params<SignInPayload & { req: any; res: any }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;
      const { formFields, req, res } = payload;

      // Convert array into object: { email: "value", password: "value" }
      const fields = formFields.reduce((acc: any, field: any) => {
        acc[field.id] = field.value;
        return acc;
      }, {});

      const { email, password } = fields;

      if (!email || !password) {
        throw new ServiceError("Email and password are required", "VALIDATION_ERROR", 400);
      }

      try {
        const response = await EmailPassword.signIn("public", email, password);

        if (response.status === "OK") {
          const recipeUserId = response.recipeUserId;
          const user = response.user;

          // Create new session
          await Session.createNewSession(req, res, "public", recipeUserId, {}, {}, {});

          const metadata = await UserMetadata.getUserMetadata(user.id);

          // Update last login
          await this.authRepository.updateLastLogin(user.id);

          return {
            status: "OK",
            user: {
              id: user.id,
              email: user.emails[0],
              timeJoined: user.timeJoined,
              metadata: metadata.metadata,
            },
          };
        } else if (response.status === "WRONG_CREDENTIALS_ERROR") {
          throw new ServiceError("Invalid credentials", "WRONG_CREDENTIALS_ERROR", 401);
        } else if (response.status === "EMAIL_NOT_VERIFIED_ERROR") {
          throw new ServiceError("Email not verified", "EMAIL_NOT_VERIFIED_ERROR", 403);
        }

        throw new ServiceError("Sign in failed", "SIGNIN_ERROR", 500);
      } catch (error: any) {
        if (error instanceof ServiceError) {
          throw error;
        }
        throw new ServiceError("Sign in failed", "SIGNIN_ERROR", 500, error);
      }
    });
  }

  public async signUp(params: Service.Params<SignUpPayload>) {
    return this.execute(params, async (params) => {
      const { payload } = params;
      const { formFields } = payload;

      // Convert array into object
      const fields = formFields.reduce((acc: any, field: any) => {
        acc[field.id] = field.value;
        return acc;
      }, {});

      const { email, password, metadata } = fields;

      if (!email || !password) {
        throw new ServiceError("Email and password are required", "VALIDATION_ERROR", 400);
      }

      try {
        // Step 1: Create the user
        const response = await EmailPassword.signUp("public", email, password);

        if (response.status === "OK") {
          const user = response.user;

          // Step 2: Store metadata (if provided)
          if (metadata) {
            await UserMetadata.updateUserMetadata(user.id, metadata);
          }

          // Fetch full metadata again to return
          const fullMetadata = await UserMetadata.getUserMetadata(user.id);

          return {
            status: "OK",
            user: {
              id: user.id,
              email: user.emails[0],
              timeJoined: user.timeJoined,
              metadata: fullMetadata.metadata,
            },
          };
        } else if (response.status === "EMAIL_ALREADY_EXISTS_ERROR") {
          throw new ServiceError("Email already exists", "EMAIL_ALREADY_EXISTS_ERROR", 400);
        }

        throw new ServiceError("Sign up failed", "SIGNUP_ERROR", 500);
      } catch (error: any) {
        if (error instanceof ServiceError) {
          throw error;
        }
        throw new ServiceError("Sign up failed", "SIGNUP_ERROR", 500, error);
      }
    });
  }

  public async signOut(params: Service.Params<{ session: any }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;
      const { session } = payload;

      try {
        await session.revokeSession();
        return { status: "OK" };
      } catch (error: any) {
        throw new ServiceError("Sign out failed", "SIGNOUT_ERROR", 500, error);
      }
    });
  }
}
