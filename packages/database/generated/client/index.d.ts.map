{"version": 3, "file": "index.d.ts", "sources": ["../../src/client.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\ndeclare global {\n  // eslint-disable-next-line no-var\n  var __prisma: PrismaClient | undefined;\n}\n\nexport const prisma =\n  globalThis.__prisma ||\n  new PrismaClient({\n    log: [\"query\", \"error\", \"warn\"],\n  });\n\nif (process.env.NODE_ENV !== \"production\") {\n  globalThis.__prisma = prisma;\n}\n\nexport type Database = typeof prisma;\n"], "names": [], "mappings": ";;AACA;AACA;AACA;AACO;AACA;;;"}