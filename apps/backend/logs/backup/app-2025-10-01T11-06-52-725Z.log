{"level":"info","time":"2025-10-01T11:04:18.313Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","msg":"Logger middleware initialized"}
{"level":"info","time":"2025-10-01T11:04:18.665Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","port":3005,"msg":"Server started"}
{"level":"info","time":"2025-10-01T11:05:32.022Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"1c7ab549-45f9-48c3-8b34-641d6aa1fd0f","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:05:32.372Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:05:32.375Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"354ms","contentLength":"262","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot destructure property 'limit' of 'payload' as it is undefined.\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":1}}"}
{"level":"info","time":"2025-10-01T11:06:50.496Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","type":"request","method":"POST","url":"/api/tenant/list","path":"/api/tenant/list","headers":{"cookie":"_gcl_au=1.1.592648881.1759242967; _ga=GA1.1.959934655.1759242967; _gid=GA1.1.521940185.1759242967; _clck=13iw2uh%5E2%5Efzr%5E0%5E2099; _fbp=fb.0.1759242967977.771830592565420626; _ga_R0ZD93E0V9=GS2.1.s1759242967$o1$g1$t1759243050$j53$l0$h0; _clsk=7cksh8%5E1759243050561%5E7%5E1%5Eb.clarity.ms%2Fcollect; sAccessToken=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; st-access-token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sFrontToken=eyJ1aWQiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJhdGUiOjE3NTkzMTk0OTMwMDAsInVwIjp7ImlhdCI6MTc1OTMxNTg5MywiZXhwIjoxNzU5MzE5NDkzLCJzdWIiOiJhMmEzZmMzZC1jMDU0LTQ5MjktYWMwOS0xYjdjZTJmZDRiOWQiLCJ0SWQiOiJwdWJsaWMiLCJyc3ViIjoiYTJhM2ZjM2QtYzA1NC00OTI5LWFjMDktMWI3Y2UyZmQ0YjlkIiwic2Vzc2lvbkhhbmRsZSI6IjgyYzI1YzIzLTE3NjQtNGQyZC1hMjExLTBkZmU4NzVkMjlhMyIsInJlZnJlc2hUb2tlbkhhc2gxIjoiNjcxOTdiODY4MzA2M2NhNDUwY2NjMGE4","user-agent":"PostmanRuntime/7.47.1","accept":"*/*","postman-token":"b2888e85-e616-48fa-80a7-f772cfaa6977","host":"localhost:3005","accept-encoding":"gzip, deflate, br","connection":"keep-alive","content-length":"0"},"query":{},"ip":"::1","userAgent":"PostmanRuntime/7.47.1"}
{"level":"info","time":"2025-10-01T11:06:50.692Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","msg":"Listing tenants"}
{"level":"info","time":"2025-10-01T11:06:50.693Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","type":"response","method":"POST","url":"/list","statusCode":200,"duration":"197ms","contentLength":"262","responseBody":"{\"message\":\"list tenants\",\"result\":{\"success\":false,\"code\":\"UNKNOWN_ERROR\",\"message\":\"Cannot destructure property 'limit' of 'payload' as it is undefined.\",\"error\":{\"name\":\"ServiceError\",\"code\":\"UNKNOWN_ERROR\",\"status\":500,\"originalError\":{}},\"executionTime\":0}}"}
{"level":"info","time":"2025-10-01T11:06:52.718Z","pid":16822,"hostname":"Anoojs-MacBook-Air.local","signal":"SIGINT","msg":"Shutdown initiated"}
