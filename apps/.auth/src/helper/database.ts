import { initializeDatabase, type DatabaseConfig } from "@repo/database";

export const initializeDatabaseConnection = async () => {
  const dbConfig: DatabaseConfig = {
    user: process.env.POSTGRESQL_USER || "azure_root",
    password: process.env.POSTGRESQL_PASSWORD || "Zeref@2111",
    host: process.env.POSTGRESQL_HOST || "testserver96.postgres.database.azure.com",
    port: parseInt(process.env.POSTGRESQL_PORT || "5432"),
    database: process.env.POSTGRESQL_DATABASE_NAME || "postgres",
    schema: process.env.POSTGRESQL_TABLE_SCHEMA || "app",
  };

  await initializeDatabase(dbConfig);
};
