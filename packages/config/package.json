{"name": "@repo/config", "version": "1.0.0", "private": true, "files": ["dist"], "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "exports": {".": {"import": {"types": "./dist/es/index.d.ts", "default": "./dist/es/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "bunchee", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0"}, "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "dotenv": "^16.4.5"}, "devDependencies": {"bunchee": "^6.4.0", "@types/node": "^22.7.5", "tsup": "^8.3.0", "@repo/typescript-config": "*", "typescript": "^5.6.3"}}