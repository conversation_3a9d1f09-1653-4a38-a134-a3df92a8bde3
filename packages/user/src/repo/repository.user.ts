import { BaseRepository } from "@repo/abstract";

export class UserRepository extends BaseRepository<any> {
  constructor() {
    super("user");
  }

  public async findByEmail(email: string): Promise<any | null> {
    const client = this.getClient();
    return client.user.findUnique({
      where: { emailId: email },
    });
  }

  public async findById(id: string): Promise<any | null> {
    const client = this.getClient();
    return client.user.findUnique({
      where: { id },
      include: {
        tenant: true,
      },
    });
  }

  public async findAll(): Promise<any[]> {
    const client = this.getClient();
    return client.user.findMany({
      include: {
        tenant: true,
      },
    });
  }

  public async createUser(userData: any): Promise<any> {
    const client = this.getClient();
    return client.user.create({
      data: userData,
    });
  }

  public async updateUser(id: string, userData: any): Promise<any> {
    const client = this.getClient();
    return client.user.update({
      where: { id },
      data: userData,
    });
  }

  public async deleteUser(id: string): Promise<any> {
    const client = this.getClient();
    return client.user.delete({
      where: { id },
    });
  }

  public async block(userId: string[]): Promise<void> {
    const client = this.getClient();
    return client.$transaction(async (tx: any) => {
      await tx.user.updateMany({
        where: {
          id: { in: userId },
          tenantId: this.getTenantId(),
        },
        data: { isActive: false },
      });
    });
  }

  public async activate(userId: string): Promise<any> {
    const client = this.getClient();
    return client.user.update({
      where: { id: userId },
      data: { isActive: true },
    });
  }

  public async deactivate(userId: string): Promise<any> {
    const client = this.getClient();
    return client.user.update({
      where: { id: userId },
      data: { isActive: false },
    });
  }

  public async findByTenant(tenantId: string): Promise<any[]> {
    const client = this.getClient();
    return client.user.findMany({
      where: { tenantId },
      include: {
        tenant: true,
      },
    });
  }

  public async countByTenant(tenantId: string): Promise<number> {
    const client = this.getClient();
    return client.user.count({
      where: { tenantId },
    });
  }

  public async listUsersRaw(args: any): Promise<any[]> {
    const client = this.getClient();
    return client.user.findMany(args);
  }
}
