# Backend Quick Start Guide

## Overview

The backend application now has a fully integrated Pino logger with automatic log rotation and cloud storage capabilities.

## Getting Started

### 1. Install Dependencies

```bash
cd cadetlabs
pnpm install
```

### 2. Build the Logger Package

```bash
cd packages/logger-pino
pnpm build
```

### 3. Build the Backend

```bash
cd apps/backend
pnpm build
```

### 4. Set Up Environment Variables

Create a `.env` file in `cadetlabs/apps/backend/`:

```bash
# Application
NODE_ENV=development
PORT=3006

# Azure Storage (optional for development)
# AZURE_STORAGE_CONNECTION_STRING=your_connection_string
# AZURE_CONTAINER_NAME=application-logs
```

### 5. Run the Backend

**Development mode** (with auto-reload):
```bash
cd apps/backend
pnpm dev
```

**Production mode**:
```bash
cd apps/backend
pnpm start
```

## Testing the Logger

### 1. Check Health Endpoint

```bash
curl http://localhost:3006/health
```

Expected response:
```json
{
  "success": true,
  "message": "Backend service is healthy",
  "timestamp": "2025-09-30T..."
}
```

### 2. Test User Endpoints

**Get all users** (demonstrates info logging):
```bash
curl http://localhost:3006/api/users
```

**Get user by ID** (demonstrates debug and warn logging):
```bash
curl http://localhost:3006/api/users/1
curl http://localhost:3006/api/users/999  # Not found - triggers warn log
```

**Create user** (demonstrates info and validation logging):
```bash
curl -X POST http://localhost:3006/api/users \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>"}'
```

**Update user** (demonstrates debug logging):
```bash
curl -X PUT http://localhost:3006/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"name":"Jane Doe","email":"<EMAIL>"}'
```

**Delete user** (demonstrates warn logging):
```bash
curl -X DELETE http://localhost:3006/api/users/1
```

### 3. Test Tenant Endpoints

**Get all tenants**:
```bash
curl http://localhost:3006/api/tenants
```

**Get tenant by ID**:
```bash
curl http://localhost:3006/api/tenants/1
```

**Create tenant**:
```bash
curl -X POST http://localhost:3006/api/tenants \
  -H "Content-Type: application/json" \
  -d '{"name":"Acme Corp","status":"active"}'
```

**Update tenant status**:
```bash
curl -X PATCH http://localhost:3006/api/tenants/1/status \
  -H "Content-Type: application/json" \
  -d '{"status":"inactive"}'
```

## Viewing Logs

### Development Mode (LocalDriver)

Logs are stored in two locations:

1. **Active log file**: `cadetlabs/apps/backend/logs/app.log`
2. **Rotated logs**: `cadetlabs/apps/backend/logs/backup/app-*.log`

View active logs:
```bash
tail -f logs/app.log
```

View rotated logs:
```bash
ls -lh logs/backup/
cat logs/backup/app-2025-09-30T12-00-00-000Z.log
```

### Production Mode (AzureBlobDriver)

Logs are uploaded to Azure Blob Storage in the configured container under the `logs/` prefix.

## Log Rotation

Logs are automatically rotated based on:

1. **Size**: When the log file exceeds 10 MB (configurable)
2. **Time**: Every 5 minutes (configurable)

When rotation occurs:
- The current log file is uploaded to storage
- The local log file is deleted
- A new log file is created

## Understanding the Logs

### Request Logs

```json
{
  "level": "info",
  "time": "2025-09-30T12:00:00.000Z",
  "type": "request",
  "method": "GET",
  "url": "/api/users",
  "path": "/api/users",
  "headers": {...},
  "query": {},
  "body": {},
  "ip": "::1",
  "userAgent": "curl/7.64.1"
}
```

### Response Logs

```json
{
  "level": "info",
  "time": "2025-09-30T12:00:00.100Z",
  "type": "response",
  "method": "GET",
  "url": "/api/users",
  "statusCode": 200,
  "duration": "100ms",
  "contentLength": "123",
  "responseBody": {...}
}
```

### Application Logs

```json
{
  "level": "info",
  "time": "2025-09-30T12:00:00.050Z",
  "msg": "Fetching all users",
  "query": {},
  "userId": "123"
}
```

## Customizing the Logger

### Change Log Level

Edit `cadetlabs/apps/backend/src/app.ts`:

```typescript
PinoLogger.initialize({
  driver: localDriver,
  logDirectory: './logs',
  maxLogSizeMB: 10,
  level: 'debug', // Change to: 'debug', 'info', 'warn', 'error'
  rotationIntervalMinutes: 5,
});
```

### Change Rotation Settings

```typescript
PinoLogger.initialize({
  driver: localDriver,
  logDirectory: './logs',
  maxLogSizeMB: 50, // Increase max size to 50 MB
  level: 'info',
  rotationIntervalMinutes: 15, // Rotate every 15 minutes
});
```

### Use Azure Storage in Development

1. Get your Azure Storage connection string
2. Add to `.env`:
   ```bash
   AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=...
   AZURE_CONTAINER_NAME=dev-logs
   ```
3. Change `NODE_ENV` to `production` or modify the driver selection logic

## Adding Logging to New Routes

```typescript
import { Router, Request, Response } from 'express';
import { PinoLogger } from '@repo/logger-pino';

const router = Router();

router.get('/my-endpoint', async (req: Request, res: Response) => {
  try {
    // Log the operation
    PinoLogger.info('Processing my endpoint', { 
      userId: req.headers['x-user-id'],
      query: req.query 
    });

    // Your business logic here
    const result = await doSomething();

    // Log success
    PinoLogger.info('Operation completed', { result });

    res.json({ success: true, data: result });
  } catch (error) {
    // Log errors
    PinoLogger.error('Operation failed', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

export default router;
```

## Troubleshooting

### Logs not appearing

1. Check if logger is initialized:
   ```typescript
   // Should see this in console on startup
   ✓ Logger initialized with LocalDriver
   ```

2. Check log directory exists:
   ```bash
   ls -la logs/
   ```

3. Check file permissions:
   ```bash
   chmod -R 755 logs/
   ```

### Logs not rotating

1. Check rotation interval is not too long
2. Check max log size is not too large
3. Verify logs are being written (check file size)
4. Check console for rotation messages:
   ```
   [Logger] Time-based rotation triggered (5.23 MB)
   [Logger] ✓ Log rotated: uploaded to server & local file deleted
   ```

### Azure upload failing

1. Verify connection string is correct
2. Check network connectivity
3. Verify container name is valid
4. Check console for error messages:
   ```
   [AzureDriver] Upload failed: ...
   ```

## Next Steps

1. **Add Real Business Logic**: Replace the example routes with your actual API endpoints
2. **Add Database**: Integrate with your database and log queries
3. **Add Authentication**: Integrate with SuperTokens and log auth events
4. **Add Monitoring**: Set up log monitoring and alerting
5. **Add Tests**: Write tests for your routes and logger integration

## Resources

- [Pino Documentation](https://getpino.io/)
- [Azure Blob Storage Documentation](https://docs.microsoft.com/en-us/azure/storage/blobs/)
- [Logger Package README](../../packages/logger-pino/README.md)
- [Migration Summary](../../LOGGER_MIGRATION_SUMMARY.md)

