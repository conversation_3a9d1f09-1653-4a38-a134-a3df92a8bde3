{"name": "@repo/model-transformer", "version": "0.0.0", "private": true, "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.js"}}}, "scripts": {"build": "tsup", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0"}, "dependencies": {"@types/express": "^4.17.17", "@types/node": "^20.0.0", "express": "^4.18.2", "supertokens-node": "23.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "@repo/typescript-config": "workspace:*", "@repo/abstract": "workspace:*", "http-status-codes": "2.3.0", "reflect-metadata": "^0.1.13"}, "peerDependencies": {}, "devDependencies": {"tsup": "^8.2.4", "eslint": "^9.31.0", "jest": "^29.7.0", "typescript": "5.8.2", "jest-environment-jsdom": "^29.7.0", "@repo/typescript-config": "workspace:*", "@types/node": "^20.0.0"}}