import "reflect-metadata";

import { Base<PERSON>ontroller } from "@repo/abstract";
import { type Request, type Response, Router } from "express";
import { EmailService } from "../service/service.email";
import { Specification } from "@repo/model-transformer";
import { SendEmailVerificationDto } from "../model/model.email.send";
import { VerifyEmailDto } from "../model/model.email.verify";

export class EmailController extends BaseController {
  private readonly router: Router;
  private service: EmailService;

  constructor() {
    super();
    this.router = Router();
    this.service = new EmailService();
  }

  @Specification({ param_blueprint: SendEmailVerificationDto }, { validate: true })
  async sendEmailVerification(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.sendEmailVerification({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: req.body,
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  @Specification({ param_blueprint: VerifyEmailDto }, { validate: true })
  async verifyEmailToken(req: Request, res: Response): Promise<Response> {
    try {
      const result = await this.service.verifyEmailToken({
        requestId: (req.headers["x-request-id"] as string) || "default-request-id",
        payload: req.body,
        context: {},
        metadata: {},
      });

      return res.json(result);
    } catch (error: any) {
      const statusCode = error.status || 500;
      return res.status(statusCode).json({ error: error.message });
    }
  }

  // Static method for backward compatibility
  static async sendEmailVerificationToUserByEmail(email: string) {
    const emailService = new EmailService();
    return emailService.sendEmailVerificationToUserByEmail(email);
  }

  // Standard CRUD methods (not used for email but required by BaseController)
  override async create(req: Request, res: Response): Promise<Response> {
    return this.sendEmailVerification(req, res);
  }

  override async get(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  override async find(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  override async update(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  override async delete(req: Request, res: Response): Promise<Response> {
    return res.status(404).json({ error: "Not implemented" });
  }

  getRouter(): Router {
    // Email management routes to match original endpoints
    this.router.post("/verify/send", this.sendEmailVerification.bind(this));
    this.router.post("/verify", this.verifyEmailToken.bind(this));

    return this.router;
  }
}
