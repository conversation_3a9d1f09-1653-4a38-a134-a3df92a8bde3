{"name": "web", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3000 --turbopack", "build": "next build && cp -r .next/standalone/apps/web/. .next/standalone && rm -rf .next/standalone/apps && cp -r .next/static .next/standalone/.next/ && cp -r public .next/standalone/", "start:standalone": "node .next/standalone/server.js", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@repo/ui": "*", "@tanstack/react-query": "^5.89.0", "axios": "^1.12.2", "fs": "0.0.1-security", "handlebars": "^4.7.8", "next": "^15.4.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.7", "supertokens-auth-react": "^0.50.0", "zod": "^3.25.76", "@tanstack/react-table": "^8.21.3", "dayjs": "^1.11.18", "jotai": "^2.14.0", "react-hook-form": "^7.62.0"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.6", "@repo/eslint-config": "*", "@repo/typescript-config": "*", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^22.15.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "autoprefixer": "^10.4.20", "eslint": "^9.33.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "5.9.2"}}